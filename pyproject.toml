[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "dims-erp"
version = "0.1.0"
description = "نظام ERP شامل لإدارة قطع غيار السيارات مع دعم متعدد اللغات ونظام مزامنة هجين"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Dims ERP Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Dims ERP Team", email = "<EMAIL>"}
]
keywords = ["erp", "auto-parts", "arabic", "b2b", "inventory", "pos"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business",
    "Topic :: Office/Business :: Financial",
    "Natural Language :: Arabic",
    "Natural Language :: English",
]
requires-python = ">=3.11"
dependencies = [
    "flet>=0.21.0",
    "fastapi>=0.104.0",
    "sqlalchemy>=2.0.0",
    "pydantic>=2.5.0",
    "loguru>=0.7.0",
    "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.10.0",
    "ruff>=0.1.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocs-mermaid2-plugin>=1.1.0",
]
integrations = [
    "twilio>=8.10.0",
    "redis>=5.0.0",
    "psycopg2-binary>=2.9.0",
]

[project.urls]
Homepage = "https://github.com/your-org/dims-erp"
Documentation = "https://dims-erp.readthedocs.io"
Repository = "https://github.com/your-org/dims-erp.git"
"Bug Tracker" = "https://github.com/your-org/dims-erp/issues"

[project.scripts]
dims-erp = "src.main:main"
dims-server = "src.api.server:main"
dims-p2p = "src.sync.p2p_server:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.json", "*.yaml", "*.yml", "*.toml", "*.po", "*.mo"]

# Black Configuration
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# Ruff Configuration
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]

# MyPy Configuration
[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "flet.*",
    "twilio.*",
    "qrcode.*",
    "barcode.*",
]
ignore_missing_imports = true

# Pytest Configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "performance: marks tests as performance tests",
]

# Coverage Configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"

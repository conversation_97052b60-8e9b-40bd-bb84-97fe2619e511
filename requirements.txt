# Core Framework
flet>=0.21.0
flet-core>=0.21.0

# Web Framework & API
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=12.0

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0
psycopg2-binary>=2.9.0
sqlite3  # Built-in with Python

# Authentication & Security
pyjwt>=2.8.0
bcrypt>=4.0.0
cryptography>=41.0.0
passlib[bcrypt]>=1.7.0

# Data Validation & Serialization
pydantic>=2.5.0
pydantic-settings>=2.1.0
marshmallow>=3.20.0

# HTTP Requests
httpx>=0.25.0
requests>=2.31.0
aiohttp>=3.9.0

# Date & Time
python-dateutil>=2.8.0
pytz>=2023.3

# Internationalization
babel>=2.13.0
flufl.i18n>=4.1.0

# File Processing
openpyxl>=3.1.0
xlsxwriter>=3.1.0
reportlab>=4.0.0
pillow>=10.0.0
qrcode[pil]>=7.4.0

# Logging
loguru>=0.7.0
structlog>=23.2.0

# Configuration
python-dotenv>=1.0.0
pyyaml>=6.0.0
toml>=0.10.0

# Async & Concurrency
asyncio-mqtt>=0.16.0
aiofiles>=23.2.0
asyncpg>=0.29.0

# Networking & P2P
zeroconf>=0.131.0
upnpclient>=0.0.8

# Message Queue & Cache
redis>=5.0.0
celery>=5.3.0

# WhatsApp Integration
twilio>=8.10.0
whatsapp-business-api>=0.0.10

# Email
aiosmtplib>=3.0.0
email-validator>=2.1.0

# PDF Generation
weasyprint>=60.0
jinja2>=3.1.0

# Barcode & QR
python-barcode>=0.15.0
qrcode>=7.4.0

# Data Processing
pandas>=2.1.0
numpy>=1.25.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
httpx-mock>=0.10.0

# Development Tools
black>=23.10.0
ruff>=0.1.0
mypy>=1.7.0
pre-commit>=3.5.0

# Monitoring & Performance
psutil>=5.9.0
memory-profiler>=0.61.0

# Utilities
click>=8.1.0
rich>=13.7.0
tqdm>=4.66.0
python-slugify>=8.0.0

# Saudi Arabia Specific
hijri-converter>=2.3.0
arabic-reshaper>=3.0.0
python-bidi>=0.4.0

# Development Dependencies (optional)
# Uncomment for development
# jupyter>=1.0.0
# ipython>=8.17.0
# notebook>=7.0.0

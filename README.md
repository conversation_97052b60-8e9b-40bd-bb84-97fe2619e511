# نظام ERP قطع الغيار - Dims ERP

## نظرة عامة
نظام ERP شامل لإدارة قطع غيار السيارات مع دعم كامل للغة العربية والإنجليزية، ونظام مزامنة هجين، ومنصة اقتصاد تشاركي B2B.

## الميزات الرئيسية
- 🌐 **متعدد اللغات**: دعم كامل للعربية والإنجليزية مع RTL/LTR
- 🎨 **ثيمات متعددة**: فاتح/داكن مع إمكانية التخصيص
- 🔄 **مزامنة هجينة**: P2P + Central Server
- 🔧 **نظام OEM**: إدارة أرقام القطع الأصلية والبديلة
- 🤝 **اقتصاد تشاركي**: منصة B2B لتبادل قطع الغيار
- 📱 **تكاملات**: WhatsApp, ZATCA, Email
- 🔐 **أمان متقدم**: JWT, تشفير, صلاحيات متدرجة

## الوحدات (13 وحدة)
1. **الرئيسية** - لوحة معلومات تفاعلية
2. **المبيعات** - POS, فواتير, عروض أسعار
3. **المشتريات** - أوامر شراء, موردين
4. **المخزون** - منتجات, OEM, حركة مخزون
5. **المالية** - حسابات, صندوق, بنوك
6. **الاقتصاد التشاركي** - سوق B2B مشترك
7. **التقارير** - تقارير شاملة وضريبية
8. **الموارد البشرية** - موظفين, مستخدمين
9. **الإعدادات** - شركة, نظام, تكاملات
10. **الأدوات** - حاسبات, باركود, استيراد/تصدير
11. **الاتصالات** - رسائل, إشعارات, WhatsApp
12. **الأمان** - سجلات, تدقيق, صلاحيات
13. **المساعدة** - أدلة, دعم, تحديثات

## التقنيات المستخدمة
- **Frontend**: Flet (Python UI Framework)
- **Backend**: FastAPI + SQLAlchemy
- **Database**: SQLite (محلي) + PostgreSQL (مركزي)
- **Sync**: P2P + WebSocket + REST API
- **Auth**: JWT + bcrypt
- **i18n**: Custom translation system
- **Testing**: pytest + coverage

## التثبيت والتشغيل

### المتطلبات
- Python 3.11+
- PostgreSQL 14+ (للخادم المركزي)
- Redis (للمزامنة)

### التثبيت
```bash
# استنساخ المشروع
git clone https://github.com/your-org/dims-erp.git
cd dims-erp

# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows

# تثبيت التبعيات
pip install -r requirements.txt

# إعداد قاعدة البيانات
python scripts/setup_db.py

# توليد بيانات تجريبية (اختياري)
python scripts/generate_demo_data.py
```

### التشغيل
```bash
# تشغيل التطبيق
python src/main.py

# تشغيل بالعربية والثيم الداكن
python src/main.py --lang=ar --theme=dark

# تشغيل بوضع التطوير
python src/main.py --dev

# تشغيل الخادم المركزي
uvicorn src.api.server:app --reload

# تشغيل خادم P2P
python src/sync/p2p_server.py
```

## البنية المعمارية

```
src/
├── ui/                 # واجهة المستخدم (Flet)
│   ├── pages/         # صفحات التطبيق
│   ├── components/    # مكونات قابلة للإعادة
│   ├── themes/        # ثيمات التطبيق
│   └── layouts/       # تخطيطات الصفحات
├── business/          # منطق الأعمال
│   ├── services/      # خدمات الأعمال
│   ├── models/        # نماذج البيانات
│   └── validators/    # التحقق من البيانات
├── data/              # طبقة البيانات
│   ├── repositories/  # مستودعات البيانات
│   ├── migrations/    # هجرات قاعدة البيانات
│   └── schemas/       # مخططات البيانات
├── integration/       # التكاملات الخارجية
│   ├── whatsapp/      # تكامل WhatsApp
│   ├── zatca/         # تكامل ZATCA
│   └── email/         # تكامل البريد الإلكتروني
├── sync/              # نظام المزامنة
│   ├── p2p/           # مزامنة P2P
│   ├── central/       # مزامنة مركزية
│   └── queue/         # طابور المزامنة
├── utils/             # أدوات مساعدة
├── config/            # إعدادات التطبيق
└── main.py            # نقطة دخول التطبيق
```

## الاختبارات
```bash
# تشغيل جميع الاختبارات
pytest

# اختبارات مع تغطية
pytest --cov=src tests/

# اختبارات الأداء
pytest tests/performance/

# اختبارات التكامل
pytest tests/integration/
```

## المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم
- 📧 Email: <EMAIL>
- 💬 WhatsApp: +966501234567
- 📖 Documentation: [docs/](docs/)
- 🐛 Issues: [GitHub Issues](https://github.com/your-org/dims-erp/issues)

## الحالة
🚧 **قيد التطوير النشط** - الإصدار الحالي: v0.1.0-alpha

"""
Central notification management system.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.orm import Session
from loguru import logger

from ..config import get_settings
from .email_service import EmailService
from .sms_service import SMSService
from .push_notifications import PushNotificationService
from ..integrations.whatsapp import WhatsAppIntegration


class NotificationType(Enum):
    """Types of notifications."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"
    REMINDER = "reminder"
    ALERT = "alert"


class NotificationChannel(Enum):
    """Notification delivery channels."""
    IN_APP = "in_app"
    EMAIL = "email"
    SMS = "sms"
    WHATSAPP = "whatsapp"
    PUSH = "push"


class NotificationPriority(Enum):
    """Notification priorities."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


@dataclass
class NotificationTemplate:
    """Notification template structure."""
    id: str
    name: str
    title_template: str
    body_template: str
    channels: List[NotificationChannel]
    priority: NotificationPriority
    category: str
    variables: List[str]
    is_active: bool = True


@dataclass
class Notification:
    """Notification structure."""
    id: str
    user_id: str
    company_id: str
    notification_type: NotificationType
    priority: NotificationPriority
    title: str
    message: str
    data: Dict[str, Any]
    channels: List[NotificationChannel]
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    sent_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    is_read: bool = False
    delivery_status: Dict[str, str] = None  # channel -> status


class NotificationManager:
    """Central notification management system."""
    
    def __init__(self, session: Session):
        self.session = session
        self.settings = get_settings()
        
        # Initialize services
        self.email_service = EmailService()
        self.sms_service = SMSService()
        self.push_service = PushNotificationService()
        self.whatsapp_service = WhatsAppIntegration()
        
        # Notification templates
        self.templates = self._load_notification_templates()
        
        # User preferences cache
        self.user_preferences: Dict[str, Dict[str, Any]] = {}
        
        # Delivery queue
        self.delivery_queue: List[Notification] = []
        
        # Background task
        self._delivery_task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start(self):
        """Start notification manager."""
        if self._running:
            return
        
        self._running = True
        logger.info("Starting notification manager")
        
        # Start background delivery task
        self._delivery_task = asyncio.create_task(self._delivery_loop())
        
        logger.info("Notification manager started")
    
    async def stop(self):
        """Stop notification manager."""
        if not self._running:
            return
        
        self._running = False
        logger.info("Stopping notification manager")
        
        # Cancel background task
        if self._delivery_task:
            self._delivery_task.cancel()
            try:
                await self._delivery_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Notification manager stopped")
    
    async def send_notification(self, user_id: str, company_id: str,
                              notification_type: NotificationType,
                              title: str, message: str,
                              data: Dict[str, Any] = None,
                              channels: List[NotificationChannel] = None,
                              priority: NotificationPriority = NotificationPriority.NORMAL,
                              scheduled_at: Optional[datetime] = None) -> str:
        """Send notification to user."""
        try:
            # Create notification
            notification = Notification(
                id=f"notif_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}",
                user_id=user_id,
                company_id=company_id,
                notification_type=notification_type,
                priority=priority,
                title=title,
                message=message,
                data=data or {},
                channels=channels or [NotificationChannel.IN_APP],
                created_at=datetime.utcnow(),
                scheduled_at=scheduled_at,
                delivery_status={}
            )
            
            # Apply user preferences
            notification = await self._apply_user_preferences(notification)
            
            # Store notification
            await self._store_notification(notification)
            
            # Queue for delivery
            if scheduled_at is None or scheduled_at <= datetime.utcnow():
                self.delivery_queue.append(notification)
            
            logger.info(f"Notification {notification.id} queued for user {user_id}")
            
            return notification.id
            
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return ""
    
    async def send_template_notification(self, template_id: str, user_id: str,
                                       company_id: str, variables: Dict[str, str],
                                       scheduled_at: Optional[datetime] = None) -> str:
        """Send notification using template."""
        try:
            template = self.templates.get(template_id)
            if not template or not template.is_active:
                logger.error(f"Template {template_id} not found or inactive")
                return ""
            
            # Replace variables in template
            title = self._replace_variables(template.title_template, variables)
            message = self._replace_variables(template.body_template, variables)
            
            # Send notification
            return await self.send_notification(
                user_id=user_id,
                company_id=company_id,
                notification_type=NotificationType.INFO,
                title=title,
                message=message,
                data={"template_id": template_id, "variables": variables},
                channels=template.channels,
                priority=template.priority,
                scheduled_at=scheduled_at
            )
            
        except Exception as e:
            logger.error(f"Failed to send template notification: {e}")
            return ""
    
    async def send_bulk_notification(self, user_ids: List[str], company_id: str,
                                   notification_type: NotificationType,
                                   title: str, message: str,
                                   data: Dict[str, Any] = None,
                                   channels: List[NotificationChannel] = None) -> List[str]:
        """Send notification to multiple users."""
        try:
            notification_ids = []
            
            for user_id in user_ids:
                notification_id = await self.send_notification(
                    user_id=user_id,
                    company_id=company_id,
                    notification_type=notification_type,
                    title=title,
                    message=message,
                    data=data,
                    channels=channels
                )
                
                if notification_id:
                    notification_ids.append(notification_id)
            
            logger.info(f"Bulk notification sent to {len(notification_ids)} users")
            
            return notification_ids
            
        except Exception as e:
            logger.error(f"Failed to send bulk notification: {e}")
            return []
    
    async def mark_as_read(self, notification_id: str, user_id: str) -> bool:
        """Mark notification as read."""
        try:
            # TODO: Implement database update
            logger.info(f"Notification {notification_id} marked as read by {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to mark notification as read: {e}")
            return False
    
    async def get_user_notifications(self, user_id: str, company_id: str,
                                   unread_only: bool = False,
                                   limit: int = 50) -> List[Dict[str, Any]]:
        """Get notifications for user."""
        try:
            # TODO: Implement database query
            notifications = []
            
            return notifications
            
        except Exception as e:
            logger.error(f"Failed to get user notifications: {e}")
            return []
    
    async def get_notification_statistics(self, company_id: str,
                                        date_from: Optional[datetime] = None,
                                        date_to: Optional[datetime] = None) -> Dict[str, Any]:
        """Get notification statistics."""
        try:
            # TODO: Implement statistics calculation
            stats = {
                "total_sent": 0,
                "total_delivered": 0,
                "total_read": 0,
                "delivery_rate": 0,
                "read_rate": 0,
                "by_channel": {},
                "by_type": {},
                "by_priority": {}
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get notification statistics: {e}")
            return {}
    
    async def set_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> bool:
        """Set user notification preferences."""
        try:
            self.user_preferences[user_id] = preferences
            
            # TODO: Store in database
            
            logger.info(f"Notification preferences updated for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set user preferences: {e}")
            return False
    
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get user notification preferences."""
        try:
            if user_id in self.user_preferences:
                return self.user_preferences[user_id]
            
            # TODO: Load from database
            
            # Default preferences
            return {
                "email_enabled": True,
                "sms_enabled": False,
                "whatsapp_enabled": True,
                "push_enabled": True,
                "quiet_hours": {"start": "22:00", "end": "08:00"},
                "categories": {
                    "invoices": {"email": True, "whatsapp": True},
                    "payments": {"email": True, "sms": False},
                    "inventory": {"email": False, "push": True},
                    "system": {"email": True, "push": True}
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get user preferences: {e}")
            return {}
    
    async def _delivery_loop(self):
        """Background delivery loop."""
        while self._running:
            try:
                if self.delivery_queue:
                    # Process notifications in queue
                    notifications_to_process = self.delivery_queue.copy()
                    self.delivery_queue.clear()
                    
                    for notification in notifications_to_process:
                        await self._deliver_notification(notification)
                
                # Check for scheduled notifications
                await self._process_scheduled_notifications()
                
                # Wait before next iteration
                await asyncio.sleep(10)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Delivery loop error: {e}")
                await asyncio.sleep(60)
    
    async def _deliver_notification(self, notification: Notification):
        """Deliver notification through specified channels."""
        try:
            delivery_results = {}
            
            for channel in notification.channels:
                try:
                    if channel == NotificationChannel.EMAIL:
                        result = await self._deliver_email(notification)
                    elif channel == NotificationChannel.SMS:
                        result = await self._deliver_sms(notification)
                    elif channel == NotificationChannel.WHATSAPP:
                        result = await self._deliver_whatsapp(notification)
                    elif channel == NotificationChannel.PUSH:
                        result = await self._deliver_push(notification)
                    elif channel == NotificationChannel.IN_APP:
                        result = await self._deliver_in_app(notification)
                    else:
                        result = {"success": False, "error": "Unknown channel"}
                    
                    delivery_results[channel.value] = "delivered" if result.get("success") else "failed"
                    
                except Exception as e:
                    logger.error(f"Failed to deliver via {channel.value}: {e}")
                    delivery_results[channel.value] = "failed"
            
            # Update notification status
            notification.delivery_status = delivery_results
            notification.sent_at = datetime.utcnow()
            
            await self._update_notification(notification)
            
        except Exception as e:
            logger.error(f"Failed to deliver notification {notification.id}: {e}")
    
    async def _deliver_email(self, notification: Notification) -> Dict[str, Any]:
        """Deliver notification via email."""
        try:
            # Get user email
            user_email = await self._get_user_email(notification.user_id)
            if not user_email:
                return {"success": False, "error": "No email address"}
            
            # Send email
            result = await self.email_service.send_notification_email(
                to_email=user_email,
                subject=notification.title,
                message=notification.message,
                notification_type=notification.notification_type
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Email delivery failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _deliver_sms(self, notification: Notification) -> Dict[str, Any]:
        """Deliver notification via SMS."""
        try:
            # Get user phone
            user_phone = await self._get_user_phone(notification.user_id)
            if not user_phone:
                return {"success": False, "error": "No phone number"}
            
            # Send SMS
            result = await self.sms_service.send_sms(
                phone_number=user_phone,
                message=f"{notification.title}\n{notification.message}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"SMS delivery failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _deliver_whatsapp(self, notification: Notification) -> Dict[str, Any]:
        """Deliver notification via WhatsApp."""
        try:
            # Get user phone
            user_phone = await self._get_user_phone(notification.user_id)
            if not user_phone:
                return {"success": False, "error": "No phone number"}
            
            # Send WhatsApp message
            result = await self.whatsapp_service.send_text_message(
                phone_number=user_phone,
                message=f"*{notification.title}*\n\n{notification.message}"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"WhatsApp delivery failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _deliver_push(self, notification: Notification) -> Dict[str, Any]:
        """Deliver push notification."""
        try:
            # Get user device tokens
            device_tokens = await self._get_user_device_tokens(notification.user_id)
            if not device_tokens:
                return {"success": False, "error": "No device tokens"}
            
            # Send push notification
            result = await self.push_service.send_notification(
                device_tokens=device_tokens,
                title=notification.title,
                body=notification.message,
                data=notification.data
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Push notification delivery failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _deliver_in_app(self, notification: Notification) -> Dict[str, Any]:
        """Store in-app notification."""
        try:
            # In-app notifications are stored in database for display in UI
            # This is handled by _store_notification
            return {"success": True}
            
        except Exception as e:
            logger.error(f"In-app notification storage failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _apply_user_preferences(self, notification: Notification) -> Notification:
        """Apply user preferences to notification."""
        try:
            preferences = await self.get_user_preferences(notification.user_id)
            
            # Filter channels based on preferences
            filtered_channels = []
            for channel in notification.channels:
                if channel == NotificationChannel.EMAIL and preferences.get("email_enabled", True):
                    filtered_channels.append(channel)
                elif channel == NotificationChannel.SMS and preferences.get("sms_enabled", False):
                    filtered_channels.append(channel)
                elif channel == NotificationChannel.WHATSAPP and preferences.get("whatsapp_enabled", True):
                    filtered_channels.append(channel)
                elif channel == NotificationChannel.PUSH and preferences.get("push_enabled", True):
                    filtered_channels.append(channel)
                elif channel == NotificationChannel.IN_APP:
                    filtered_channels.append(channel)  # Always include in-app
            
            notification.channels = filtered_channels
            
            # Check quiet hours
            quiet_hours = preferences.get("quiet_hours")
            if quiet_hours and notification.priority != NotificationPriority.URGENT:
                current_time = datetime.utcnow().time()
                start_time = datetime.strptime(quiet_hours["start"], "%H:%M").time()
                end_time = datetime.strptime(quiet_hours["end"], "%H:%M").time()
                
                if start_time <= current_time <= end_time:
                    # Schedule for after quiet hours
                    tomorrow = datetime.utcnow().replace(hour=int(quiet_hours["end"].split(":")[0]),
                                                       minute=int(quiet_hours["end"].split(":")[1]),
                                                       second=0, microsecond=0)
                    if tomorrow <= datetime.utcnow():
                        tomorrow += timedelta(days=1)
                    
                    notification.scheduled_at = tomorrow
            
            return notification
            
        except Exception as e:
            logger.error(f"Failed to apply user preferences: {e}")
            return notification
    
    def _replace_variables(self, template: str, variables: Dict[str, str]) -> str:
        """Replace variables in template."""
        result = template
        for key, value in variables.items():
            result = result.replace(f"{{{key}}}", str(value))
        return result
    
    def _load_notification_templates(self) -> Dict[str, NotificationTemplate]:
        """Load notification templates."""
        templates = {
            "invoice_created": NotificationTemplate(
                id="invoice_created",
                name="Invoice Created",
                title_template="فاتورة جديدة رقم {invoice_number}",
                body_template="تم إنشاء فاتورة جديدة رقم {invoice_number} بمبلغ {total_amount} {currency}",
                channels=[NotificationChannel.IN_APP, NotificationChannel.EMAIL],
                priority=NotificationPriority.NORMAL,
                category="invoices",
                variables=["invoice_number", "total_amount", "currency"]
            ),
            "payment_received": NotificationTemplate(
                id="payment_received",
                name="Payment Received",
                title_template="تم استلام دفعة جديدة",
                body_template="تم استلام دفعة بمبلغ {amount} {currency} للفاتورة رقم {invoice_number}",
                channels=[NotificationChannel.IN_APP, NotificationChannel.WHATSAPP],
                priority=NotificationPriority.NORMAL,
                category="payments",
                variables=["amount", "currency", "invoice_number"]
            ),
            "low_stock_alert": NotificationTemplate(
                id="low_stock_alert",
                name="Low Stock Alert",
                title_template="تنبيه: مخزون منخفض",
                body_template="المنتج {product_name} وصل إلى الحد الأدنى. الكمية المتبقية: {current_stock}",
                channels=[NotificationChannel.IN_APP, NotificationChannel.EMAIL],
                priority=NotificationPriority.HIGH,
                category="inventory",
                variables=["product_name", "current_stock"]
            )
        }
        
        return templates
    
    # Placeholder methods for database operations
    async def _store_notification(self, notification: Notification):
        """Store notification in database."""
        # TODO: Implement database storage
        pass
    
    async def _update_notification(self, notification: Notification):
        """Update notification in database."""
        # TODO: Implement database update
        pass
    
    async def _process_scheduled_notifications(self):
        """Process scheduled notifications."""
        # TODO: Implement scheduled notification processing
        pass
    
    async def _get_user_email(self, user_id: str) -> Optional[str]:
        """Get user email address."""
        # TODO: Implement user email retrieval
        return None
    
    async def _get_user_phone(self, user_id: str) -> Optional[str]:
        """Get user phone number."""
        # TODO: Implement user phone retrieval
        return None
    
    async def _get_user_device_tokens(self, user_id: str) -> List[str]:
        """Get user device tokens for push notifications."""
        # TODO: Implement device token retrieval
        return []

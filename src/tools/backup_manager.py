"""
Comprehensive backup and restore system.
"""

import os
import shutil
import gzip
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import asyncio
from loguru import logger

from ..config import get_settings


class BackupType(Enum):
    """Types of backups."""
    FULL = "full"
    INCREMENTAL = "incremental"
    DIFFERENTIAL = "differential"
    DATABASE_ONLY = "database_only"
    FILES_ONLY = "files_only"


class BackupStatus(Enum):
    """Backup operation status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BackupJob:
    """Backup job configuration."""
    id: str
    name: str
    backup_type: BackupType
    schedule: str  # cron expression
    retention_days: int
    compress: bool
    encrypt: bool
    include_paths: List[str]
    exclude_paths: List[str]
    destination: str
    is_active: bool = True
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None


@dataclass
class BackupResult:
    """Backup operation result."""
    job_id: str
    backup_type: BackupType
    status: BackupStatus
    start_time: datetime
    end_time: Optional[datetime]
    duration: Optional[float]
    backup_path: Optional[str]
    file_size: Optional[int]
    files_count: Optional[int]
    errors: List[str]
    warnings: List[str]


class BackupManager:
    """Comprehensive backup and restore system."""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Backup configuration
        self.backup_dir = Path(self.settings.backup_location or "backups")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Database path
        self.db_path = Path(self.settings.database_url.replace("sqlite:///", ""))
        
        # Default paths to backup
        self.default_include_paths = [
            "data/",
            "uploads/",
            "logs/",
            "config/",
            str(self.db_path)
        ]
        
        # Paths to exclude
        self.default_exclude_paths = [
            "*.tmp",
            "*.log",
            "__pycache__/",
            "*.pyc",
            ".git/",
            "node_modules/"
        ]
        
        # Active backup jobs
        self.backup_jobs: Dict[str, BackupJob] = {}
        
        # Background task
        self._scheduler_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Load backup jobs
        self._load_backup_jobs()
    
    async def start(self):
        """Start backup manager."""
        if self._running:
            return
        
        self._running = True
        logger.info("Starting backup manager")
        
        # Start scheduler
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        
        logger.info("Backup manager started")
    
    async def stop(self):
        """Stop backup manager."""
        if not self._running:
            return
        
        self._running = False
        logger.info("Stopping backup manager")
        
        # Cancel scheduler
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Backup manager stopped")
    
    async def create_backup(self, job_id: str = None, 
                          backup_type: BackupType = BackupType.FULL,
                          compress: bool = True) -> BackupResult:
        """Create backup manually."""
        try:
            # Get or create job
            if job_id and job_id in self.backup_jobs:
                job = self.backup_jobs[job_id]
            else:
                job = BackupJob(
                    id=f"manual_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                    name="Manual Backup",
                    backup_type=backup_type,
                    schedule="",
                    retention_days=30,
                    compress=compress,
                    encrypt=False,
                    include_paths=self.default_include_paths,
                    exclude_paths=self.default_exclude_paths,
                    destination=str(self.backup_dir)
                )
            
            logger.info(f"Starting backup: {job.name} ({job.backup_type.value})")
            
            # Create backup result
            result = BackupResult(
                job_id=job.id,
                backup_type=job.backup_type,
                status=BackupStatus.IN_PROGRESS,
                start_time=datetime.utcnow(),
                end_time=None,
                duration=None,
                backup_path=None,
                file_size=None,
                files_count=None,
                errors=[],
                warnings=[]
            )
            
            # Perform backup based on type
            if job.backup_type == BackupType.DATABASE_ONLY:
                await self._backup_database(job, result)
            elif job.backup_type == BackupType.FILES_ONLY:
                await self._backup_files(job, result)
            else:
                # Full, incremental, or differential backup
                await self._backup_full(job, result)
            
            # Finalize result
            result.end_time = datetime.utcnow()
            result.duration = (result.end_time - result.start_time).total_seconds()
            
            if len(result.errors) == 0:
                result.status = BackupStatus.COMPLETED
                logger.info(f"Backup completed successfully: {result.backup_path}")
            else:
                result.status = BackupStatus.FAILED
                logger.error(f"Backup failed with {len(result.errors)} errors")
            
            # Update job last run
            if job_id and job_id in self.backup_jobs:
                self.backup_jobs[job_id].last_run = result.start_time
            
            # Cleanup old backups
            await self._cleanup_old_backups(job)
            
            return result
            
        except Exception as e:
            logger.error(f"Backup creation failed: {e}")
            return BackupResult(
                job_id=job_id or "unknown",
                backup_type=backup_type,
                status=BackupStatus.FAILED,
                start_time=datetime.utcnow(),
                end_time=datetime.utcnow(),
                duration=0,
                backup_path=None,
                file_size=None,
                files_count=None,
                errors=[str(e)],
                warnings=[]
            )
    
    async def restore_backup(self, backup_path: str, 
                           restore_database: bool = True,
                           restore_files: bool = True) -> Dict[str, Any]:
        """Restore from backup."""
        try:
            logger.info(f"Starting restore from: {backup_path}")
            
            backup_file = Path(backup_path)
            if not backup_file.exists():
                raise FileNotFoundError(f"Backup file not found: {backup_path}")
            
            # Create restore directory
            restore_dir = self.backup_dir / f"restore_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            restore_dir.mkdir(parents=True, exist_ok=True)
            
            # Extract backup
            if backup_file.suffix == '.gz':
                await self._extract_compressed_backup(backup_file, restore_dir)
            else:
                await self._extract_backup(backup_file, restore_dir)
            
            restore_result = {
                "success": True,
                "database_restored": False,
                "files_restored": False,
                "errors": [],
                "warnings": []
            }
            
            # Restore database
            if restore_database:
                try:
                    db_backup_path = restore_dir / "database.db"
                    if db_backup_path.exists():
                        # Backup current database
                        current_db_backup = self.db_path.with_suffix('.db.backup')
                        shutil.copy2(self.db_path, current_db_backup)
                        
                        # Restore database
                        shutil.copy2(db_backup_path, self.db_path)
                        restore_result["database_restored"] = True
                        logger.info("Database restored successfully")
                    else:
                        restore_result["warnings"].append("No database backup found")
                        
                except Exception as e:
                    restore_result["errors"].append(f"Database restore failed: {e}")
            
            # Restore files
            if restore_files:
                try:
                    files_dir = restore_dir / "files"
                    if files_dir.exists():
                        # Restore files
                        for item in files_dir.rglob("*"):
                            if item.is_file():
                                relative_path = item.relative_to(files_dir)
                                target_path = Path(relative_path)
                                target_path.parent.mkdir(parents=True, exist_ok=True)
                                shutil.copy2(item, target_path)
                        
                        restore_result["files_restored"] = True
                        logger.info("Files restored successfully")
                    else:
                        restore_result["warnings"].append("No files backup found")
                        
                except Exception as e:
                    restore_result["errors"].append(f"Files restore failed: {e}")
            
            # Cleanup restore directory
            shutil.rmtree(restore_dir, ignore_errors=True)
            
            if restore_result["errors"]:
                restore_result["success"] = False
            
            return restore_result
            
        except Exception as e:
            logger.error(f"Restore failed: {e}")
            return {
                "success": False,
                "database_restored": False,
                "files_restored": False,
                "errors": [str(e)],
                "warnings": []
            }
    
    async def schedule_backup(self, job: BackupJob) -> bool:
        """Schedule a backup job."""
        try:
            self.backup_jobs[job.id] = job
            
            # Calculate next run time
            job.next_run = self._calculate_next_run(job.schedule)
            
            # Save jobs
            self._save_backup_jobs()
            
            logger.info(f"Backup job scheduled: {job.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to schedule backup job: {e}")
            return False
    
    async def get_backup_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get backup history."""
        try:
            history = []
            
            # Scan backup directory for backup files
            for backup_file in self.backup_dir.glob("backup_*.json"):
                try:
                    with open(backup_file, 'r') as f:
                        backup_info = json.load(f)
                        history.append(backup_info)
                except Exception as e:
                    logger.warning(f"Failed to read backup info: {e}")
            
            # Sort by date (newest first)
            history.sort(key=lambda x: x.get("start_time", ""), reverse=True)
            
            return history[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get backup history: {e}")
            return []
    
    async def get_backup_statistics(self) -> Dict[str, Any]:
        """Get backup statistics."""
        try:
            stats = {
                "total_backups": 0,
                "successful_backups": 0,
                "failed_backups": 0,
                "total_size": 0,
                "last_backup": None,
                "next_scheduled": None,
                "active_jobs": len([j for j in self.backup_jobs.values() if j.is_active])
            }
            
            # Calculate statistics from backup files
            for backup_file in self.backup_dir.glob("backup_*"):
                if backup_file.is_file():
                    stats["total_backups"] += 1
                    stats["total_size"] += backup_file.stat().st_size
                    
                    # Check if it's a successful backup (has corresponding .json file)
                    info_file = backup_file.with_suffix('.json')
                    if info_file.exists():
                        try:
                            with open(info_file, 'r') as f:
                                backup_info = json.load(f)
                                if backup_info.get("status") == "completed":
                                    stats["successful_backups"] += 1
                                else:
                                    stats["failed_backups"] += 1
                                
                                # Update last backup
                                backup_time = backup_info.get("start_time")
                                if backup_time and (not stats["last_backup"] or backup_time > stats["last_backup"]):
                                    stats["last_backup"] = backup_time
                        except:
                            stats["failed_backups"] += 1
            
            # Find next scheduled backup
            next_runs = [job.next_run for job in self.backup_jobs.values() if job.is_active and job.next_run]
            if next_runs:
                stats["next_scheduled"] = min(next_runs).isoformat()
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get backup statistics: {e}")
            return {}
    
    async def _backup_database(self, job: BackupJob, result: BackupResult):
        """Backup database only."""
        try:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_db_{timestamp}.db"
            
            if job.compress:
                backup_filename += ".gz"
            
            backup_path = Path(job.destination) / backup_filename
            
            # Copy database
            if job.compress:
                with open(self.db_path, 'rb') as f_in:
                    with gzip.open(backup_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            else:
                shutil.copy2(self.db_path, backup_path)
            
            result.backup_path = str(backup_path)
            result.file_size = backup_path.stat().st_size
            result.files_count = 1
            
        except Exception as e:
            result.errors.append(f"Database backup failed: {e}")
    
    async def _backup_files(self, job: BackupJob, result: BackupResult):
        """Backup files only."""
        try:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_files_{timestamp}.tar"
            
            if job.compress:
                backup_filename += ".gz"
            
            backup_path = Path(job.destination) / backup_filename
            
            # Create tar archive
            import tarfile
            
            mode = "w:gz" if job.compress else "w"
            with tarfile.open(backup_path, mode) as tar:
                files_count = 0
                
                for include_path in job.include_paths:
                    path = Path(include_path)
                    if path.exists():
                        if path.is_file():
                            if not self._is_excluded(str(path), job.exclude_paths):
                                tar.add(path, arcname=path.name)
                                files_count += 1
                        else:
                            for file_path in path.rglob("*"):
                                if file_path.is_file() and not self._is_excluded(str(file_path), job.exclude_paths):
                                    tar.add(file_path, arcname=file_path.relative_to(path.parent))
                                    files_count += 1
            
            result.backup_path = str(backup_path)
            result.file_size = backup_path.stat().st_size
            result.files_count = files_count
            
        except Exception as e:
            result.errors.append(f"Files backup failed: {e}")
    
    async def _backup_full(self, job: BackupJob, result: BackupResult):
        """Perform full backup."""
        try:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_full_{timestamp}.tar"
            
            if job.compress:
                backup_filename += ".gz"
            
            backup_path = Path(job.destination) / backup_filename
            
            # Create tar archive
            import tarfile
            
            mode = "w:gz" if job.compress else "w"
            with tarfile.open(backup_path, mode) as tar:
                files_count = 0
                
                # Add database
                if self.db_path.exists():
                    tar.add(self.db_path, arcname="database.db")
                    files_count += 1
                
                # Add other files
                for include_path in job.include_paths:
                    if include_path == str(self.db_path):
                        continue  # Already added
                    
                    path = Path(include_path)
                    if path.exists():
                        if path.is_file():
                            if not self._is_excluded(str(path), job.exclude_paths):
                                tar.add(path, arcname=f"files/{path.name}")
                                files_count += 1
                        else:
                            for file_path in path.rglob("*"):
                                if file_path.is_file() and not self._is_excluded(str(file_path), job.exclude_paths):
                                    tar.add(file_path, arcname=f"files/{file_path.relative_to(path.parent)}")
                                    files_count += 1
            
            result.backup_path = str(backup_path)
            result.file_size = backup_path.stat().st_size
            result.files_count = files_count
            
            # Save backup info
            await self._save_backup_info(result)
            
        except Exception as e:
            result.errors.append(f"Full backup failed: {e}")
    
    async def _scheduler_loop(self):
        """Background scheduler loop."""
        while self._running:
            try:
                current_time = datetime.utcnow()
                
                # Check for scheduled backups
                for job in self.backup_jobs.values():
                    if (job.is_active and job.next_run and 
                        job.next_run <= current_time):
                        
                        # Run backup
                        logger.info(f"Running scheduled backup: {job.name}")
                        await self.create_backup(job.id)
                        
                        # Calculate next run
                        job.next_run = self._calculate_next_run(job.schedule)
                
                # Wait before next check
                await asyncio.sleep(60)  # Check every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    def _is_excluded(self, file_path: str, exclude_patterns: List[str]) -> bool:
        """Check if file should be excluded."""
        import fnmatch
        
        for pattern in exclude_patterns:
            if fnmatch.fnmatch(file_path, pattern):
                return True
        return False
    
    def _calculate_next_run(self, schedule: str) -> Optional[datetime]:
        """Calculate next run time from cron expression."""
        try:
            # Simple implementation - in production, use croniter library
            # For now, just add 24 hours for daily backups
            return datetime.utcnow() + timedelta(days=1)
            
        except Exception as e:
            logger.error(f"Failed to calculate next run: {e}")
            return None
    
    async def _cleanup_old_backups(self, job: BackupJob):
        """Clean up old backup files."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=job.retention_days)
            
            for backup_file in Path(job.destination).glob("backup_*"):
                if backup_file.stat().st_mtime < cutoff_date.timestamp():
                    backup_file.unlink()
                    
                    # Also remove corresponding .json file
                    info_file = backup_file.with_suffix('.json')
                    if info_file.exists():
                        info_file.unlink()
            
        except Exception as e:
            logger.error(f"Failed to cleanup old backups: {e}")
    
    async def _save_backup_info(self, result: BackupResult):
        """Save backup information."""
        try:
            if result.backup_path:
                backup_file = Path(result.backup_path)
                info_file = backup_file.with_suffix('.json')
                
                info_data = {
                    "job_id": result.job_id,
                    "backup_type": result.backup_type.value,
                    "status": result.status.value,
                    "start_time": result.start_time.isoformat(),
                    "end_time": result.end_time.isoformat() if result.end_time else None,
                    "duration": result.duration,
                    "file_size": result.file_size,
                    "files_count": result.files_count,
                    "errors": result.errors,
                    "warnings": result.warnings
                }
                
                with open(info_file, 'w') as f:
                    json.dump(info_data, f, indent=2)
                    
        except Exception as e:
            logger.error(f"Failed to save backup info: {e}")
    
    def _load_backup_jobs(self):
        """Load backup jobs from configuration."""
        try:
            jobs_file = self.backup_dir / "backup_jobs.json"
            if jobs_file.exists():
                with open(jobs_file, 'r') as f:
                    jobs_data = json.load(f)
                    
                    for job_data in jobs_data:
                        job = BackupJob(**job_data)
                        self.backup_jobs[job.id] = job
                        
        except Exception as e:
            logger.error(f"Failed to load backup jobs: {e}")
    
    def _save_backup_jobs(self):
        """Save backup jobs to configuration."""
        try:
            jobs_file = self.backup_dir / "backup_jobs.json"
            jobs_data = []
            
            for job in self.backup_jobs.values():
                job_dict = {
                    "id": job.id,
                    "name": job.name,
                    "backup_type": job.backup_type.value,
                    "schedule": job.schedule,
                    "retention_days": job.retention_days,
                    "compress": job.compress,
                    "encrypt": job.encrypt,
                    "include_paths": job.include_paths,
                    "exclude_paths": job.exclude_paths,
                    "destination": job.destination,
                    "is_active": job.is_active,
                    "last_run": job.last_run.isoformat() if job.last_run else None,
                    "next_run": job.next_run.isoformat() if job.next_run else None
                }
                jobs_data.append(job_dict)
            
            with open(jobs_file, 'w') as f:
                json.dump(jobs_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save backup jobs: {e}")
    
    async def _extract_compressed_backup(self, backup_file: Path, extract_dir: Path):
        """Extract compressed backup file."""
        # TODO: Implement extraction logic
        pass
    
    async def _extract_backup(self, backup_file: Path, extract_dir: Path):
        """Extract backup file."""
        # TODO: Implement extraction logic
        pass

"""
Business calculator with tax, discount, and currency calculations.
"""

from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import math


class CalculationType(Enum):
    """Types of calculations."""
    TAX = "tax"
    DISCOUNT = "discount"
    MARGIN = "margin"
    MARKUP = "markup"
    CURRENCY = "currency"
    LOAN = "loan"
    DEPRECIATION = "depreciation"
    BREAK_EVEN = "break_even"


@dataclass
class CalculationResult:
    """Calculation result structure."""
    calculation_type: CalculationType
    input_values: Dict[str, Any]
    result: Dict[str, Any]
    formula_used: str
    explanation: str


class Calculator:
    """Business calculator with various financial calculations."""
    
    def __init__(self):
        # Tax rates (can be configured)
        self.tax_rates = {
            "vat_saudi": Decimal("0.15"),  # 15% VAT in Saudi Arabia
            "vat_uae": Decimal("0.05"),    # 5% VAT in UAE
            "sales_tax": Decimal("0.10"),  # Generic sales tax
            "income_tax": Decimal("0.20")  # Generic income tax
        }
        
        # Currency exchange rates (should be updated from API)
        self.exchange_rates = {
            "USD_SAR": Decimal("3.75"),
            "EUR_SAR": Decimal("4.10"),
            "GBP_SAR": Decimal("4.65"),
            "AED_SAR": Decimal("1.02"),
            "KWD_SAR": Decimal("12.25"),
            "BHD_SAR": Decimal("9.95"),
            "QAR_SAR": Decimal("1.03"),
            "OMR_SAR": Decimal("9.75")
        }
    
    def calculate_tax(self, amount: float, tax_rate: float = None, 
                     tax_type: str = "vat_saudi", 
                     is_inclusive: bool = False) -> CalculationResult:
        """Calculate tax amount."""
        try:
            amount_decimal = Decimal(str(amount))
            
            # Get tax rate
            if tax_rate is not None:
                rate = Decimal(str(tax_rate))
            else:
                rate = self.tax_rates.get(tax_type, Decimal("0.15"))
            
            if is_inclusive:
                # Tax is included in the amount
                tax_amount = amount_decimal * rate / (Decimal("1") + rate)
                net_amount = amount_decimal - tax_amount
                formula = f"Tax = Amount × Rate ÷ (1 + Rate)"
                explanation = f"Tax of {rate*100}% is included in the amount"
            else:
                # Tax is added to the amount
                tax_amount = amount_decimal * rate
                net_amount = amount_decimal
                formula = f"Tax = Amount × Rate"
                explanation = f"Tax of {rate*100}% is added to the amount"
            
            total_amount = net_amount + tax_amount
            
            return CalculationResult(
                calculation_type=CalculationType.TAX,
                input_values={
                    "amount": float(amount_decimal),
                    "tax_rate": float(rate),
                    "is_inclusive": is_inclusive
                },
                result={
                    "net_amount": float(net_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "tax_amount": float(tax_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "total_amount": float(total_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "tax_rate_percent": float(rate * 100)
                },
                formula_used=formula,
                explanation=explanation
            )
            
        except Exception as e:
            raise ValueError(f"Tax calculation failed: {e}")
    
    def calculate_discount(self, original_price: float, discount_value: float,
                         is_percentage: bool = True) -> CalculationResult:
        """Calculate discount amount and final price."""
        try:
            original_decimal = Decimal(str(original_price))
            discount_decimal = Decimal(str(discount_value))
            
            if is_percentage:
                # Percentage discount
                discount_amount = original_decimal * (discount_decimal / Decimal("100"))
                formula = f"Discount = Original Price × (Discount % ÷ 100)"
                explanation = f"Discount of {discount_value}% applied"
            else:
                # Fixed amount discount
                discount_amount = discount_decimal
                formula = f"Discount = Fixed Amount"
                explanation = f"Fixed discount of {discount_value} applied"
            
            final_price = original_decimal - discount_amount
            discount_percentage = (discount_amount / original_decimal) * Decimal("100")
            savings = discount_amount
            
            return CalculationResult(
                calculation_type=CalculationType.DISCOUNT,
                input_values={
                    "original_price": float(original_decimal),
                    "discount_value": float(discount_decimal),
                    "is_percentage": is_percentage
                },
                result={
                    "original_price": float(original_decimal),
                    "discount_amount": float(discount_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "final_price": float(final_price.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "discount_percentage": float(discount_percentage.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "savings": float(savings.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP))
                },
                formula_used=formula,
                explanation=explanation
            )
            
        except Exception as e:
            raise ValueError(f"Discount calculation failed: {e}")
    
    def calculate_margin(self, cost_price: float, selling_price: float) -> CalculationResult:
        """Calculate profit margin."""
        try:
            cost_decimal = Decimal(str(cost_price))
            selling_decimal = Decimal(str(selling_price))
            
            profit = selling_decimal - cost_decimal
            margin_percentage = (profit / selling_decimal) * Decimal("100")
            markup_percentage = (profit / cost_decimal) * Decimal("100")
            
            return CalculationResult(
                calculation_type=CalculationType.MARGIN,
                input_values={
                    "cost_price": float(cost_decimal),
                    "selling_price": float(selling_decimal)
                },
                result={
                    "cost_price": float(cost_decimal),
                    "selling_price": float(selling_decimal),
                    "profit": float(profit.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "margin_percentage": float(margin_percentage.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "markup_percentage": float(markup_percentage.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP))
                },
                formula_used="Margin % = (Profit ÷ Selling Price) × 100",
                explanation="Profit margin as percentage of selling price"
            )
            
        except Exception as e:
            raise ValueError(f"Margin calculation failed: {e}")
    
    def calculate_markup(self, cost_price: float, markup_percentage: float) -> CalculationResult:
        """Calculate selling price based on markup."""
        try:
            cost_decimal = Decimal(str(cost_price))
            markup_decimal = Decimal(str(markup_percentage))
            
            markup_amount = cost_decimal * (markup_decimal / Decimal("100"))
            selling_price = cost_decimal + markup_amount
            margin_percentage = (markup_amount / selling_price) * Decimal("100")
            
            return CalculationResult(
                calculation_type=CalculationType.MARKUP,
                input_values={
                    "cost_price": float(cost_decimal),
                    "markup_percentage": float(markup_decimal)
                },
                result={
                    "cost_price": float(cost_decimal),
                    "markup_amount": float(markup_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "selling_price": float(selling_price.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "markup_percentage": float(markup_decimal),
                    "margin_percentage": float(margin_percentage.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP))
                },
                formula_used="Selling Price = Cost Price + (Cost Price × Markup %)",
                explanation=f"Selling price with {markup_percentage}% markup"
            )
            
        except Exception as e:
            raise ValueError(f"Markup calculation failed: {e}")
    
    def convert_currency(self, amount: float, from_currency: str, 
                        to_currency: str, custom_rate: float = None) -> CalculationResult:
        """Convert currency using exchange rates."""
        try:
            amount_decimal = Decimal(str(amount))
            
            if custom_rate is not None:
                rate = Decimal(str(custom_rate))
                rate_source = "custom"
            else:
                # Get exchange rate
                rate_key = f"{from_currency}_{to_currency}"
                reverse_key = f"{to_currency}_{from_currency}"
                
                if rate_key in self.exchange_rates:
                    rate = self.exchange_rates[rate_key]
                    rate_source = "system"
                elif reverse_key in self.exchange_rates:
                    rate = Decimal("1") / self.exchange_rates[reverse_key]
                    rate_source = "system"
                else:
                    raise ValueError(f"Exchange rate not found for {from_currency} to {to_currency}")
            
            converted_amount = amount_decimal * rate
            
            return CalculationResult(
                calculation_type=CalculationType.CURRENCY,
                input_values={
                    "amount": float(amount_decimal),
                    "from_currency": from_currency,
                    "to_currency": to_currency,
                    "exchange_rate": float(rate)
                },
                result={
                    "original_amount": float(amount_decimal),
                    "converted_amount": float(converted_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "exchange_rate": float(rate),
                    "rate_source": rate_source
                },
                formula_used="Converted Amount = Original Amount × Exchange Rate",
                explanation=f"Converted {amount} {from_currency} to {to_currency}"
            )
            
        except Exception as e:
            raise ValueError(f"Currency conversion failed: {e}")
    
    def calculate_loan_payment(self, principal: float, annual_rate: float, 
                             years: int, payment_frequency: int = 12) -> CalculationResult:
        """Calculate loan payment amount."""
        try:
            principal_decimal = Decimal(str(principal))
            annual_rate_decimal = Decimal(str(annual_rate)) / Decimal("100")
            
            # Monthly interest rate
            monthly_rate = annual_rate_decimal / Decimal(str(payment_frequency))
            
            # Total number of payments
            total_payments = years * payment_frequency
            
            if monthly_rate == 0:
                # No interest loan
                payment = principal_decimal / Decimal(str(total_payments))
            else:
                # Calculate using loan payment formula
                payment = principal_decimal * (
                    monthly_rate * (Decimal("1") + monthly_rate) ** total_payments
                ) / (
                    (Decimal("1") + monthly_rate) ** total_payments - Decimal("1")
                )
            
            total_paid = payment * Decimal(str(total_payments))
            total_interest = total_paid - principal_decimal
            
            return CalculationResult(
                calculation_type=CalculationType.LOAN,
                input_values={
                    "principal": float(principal_decimal),
                    "annual_rate": float(annual_rate),
                    "years": years,
                    "payment_frequency": payment_frequency
                },
                result={
                    "payment_amount": float(payment.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "total_payments": total_payments,
                    "total_paid": float(total_paid.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "total_interest": float(total_interest.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "monthly_rate": float(monthly_rate * 100)
                },
                formula_used="PMT = P × [r(1+r)^n] / [(1+r)^n - 1]",
                explanation=f"Loan payment for {years} years at {annual_rate}% annual rate"
            )
            
        except Exception as e:
            raise ValueError(f"Loan calculation failed: {e}")
    
    def calculate_break_even(self, fixed_costs: float, variable_cost_per_unit: float,
                           selling_price_per_unit: float) -> CalculationResult:
        """Calculate break-even point."""
        try:
            fixed_decimal = Decimal(str(fixed_costs))
            variable_decimal = Decimal(str(variable_cost_per_unit))
            selling_decimal = Decimal(str(selling_price_per_unit))
            
            contribution_margin = selling_decimal - variable_decimal
            
            if contribution_margin <= 0:
                raise ValueError("Selling price must be greater than variable cost")
            
            break_even_units = fixed_decimal / contribution_margin
            break_even_revenue = break_even_units * selling_decimal
            contribution_margin_ratio = (contribution_margin / selling_decimal) * Decimal("100")
            
            return CalculationResult(
                calculation_type=CalculationType.BREAK_EVEN,
                input_values={
                    "fixed_costs": float(fixed_decimal),
                    "variable_cost_per_unit": float(variable_decimal),
                    "selling_price_per_unit": float(selling_decimal)
                },
                result={
                    "break_even_units": float(break_even_units.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "break_even_revenue": float(break_even_revenue.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "contribution_margin": float(contribution_margin.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "contribution_margin_ratio": float(contribution_margin_ratio.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP))
                },
                formula_used="Break-even Units = Fixed Costs ÷ Contribution Margin",
                explanation="Units needed to cover all fixed costs"
            )
            
        except Exception as e:
            raise ValueError(f"Break-even calculation failed: {e}")
    
    def calculate_depreciation(self, cost: float, salvage_value: float, 
                             useful_life: int, method: str = "straight_line") -> CalculationResult:
        """Calculate depreciation using different methods."""
        try:
            cost_decimal = Decimal(str(cost))
            salvage_decimal = Decimal(str(salvage_value))
            life_decimal = Decimal(str(useful_life))
            
            depreciable_amount = cost_decimal - salvage_decimal
            
            if method == "straight_line":
                annual_depreciation = depreciable_amount / life_decimal
                formula = "Annual Depreciation = (Cost - Salvage Value) ÷ Useful Life"
                explanation = "Equal depreciation each year"
                
            elif method == "double_declining":
                rate = Decimal("2") / life_decimal
                annual_depreciation = cost_decimal * rate
                formula = "Annual Depreciation = Book Value × (2 ÷ Useful Life)"
                explanation = "Accelerated depreciation method"
                
            else:
                raise ValueError(f"Unsupported depreciation method: {method}")
            
            return CalculationResult(
                calculation_type=CalculationType.DEPRECIATION,
                input_values={
                    "cost": float(cost_decimal),
                    "salvage_value": float(salvage_decimal),
                    "useful_life": useful_life,
                    "method": method
                },
                result={
                    "annual_depreciation": float(annual_depreciation.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "depreciable_amount": float(depreciable_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)),
                    "depreciation_rate": float((annual_depreciation / cost_decimal * 100).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP))
                },
                formula_used=formula,
                explanation=explanation
            )
            
        except Exception as e:
            raise ValueError(f"Depreciation calculation failed: {e}")
    
    def update_exchange_rates(self, rates: Dict[str, float]):
        """Update currency exchange rates."""
        for pair, rate in rates.items():
            self.exchange_rates[pair] = Decimal(str(rate))
    
    def update_tax_rates(self, rates: Dict[str, float]):
        """Update tax rates."""
        for tax_type, rate in rates.items():
            self.tax_rates[tax_type] = Decimal(str(rate))
    
    def get_available_currencies(self) -> List[str]:
        """Get list of available currencies."""
        currencies = set()
        for pair in self.exchange_rates.keys():
            from_curr, to_curr = pair.split("_")
            currencies.add(from_curr)
            currencies.add(to_curr)
        return sorted(list(currencies))
    
    def get_tax_types(self) -> List[str]:
        """Get list of available tax types."""
        return list(self.tax_rates.keys())

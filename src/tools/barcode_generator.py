"""
Barcode and QR code generator for products and documents.
"""

import io
import base64
from typing import Dict, List, Any, Optional, Tu<PERSON>
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from loguru import logger

try:
    import qrcode
    from qrcode.image.styledpil import StyledPilImage
    from qrcode.image.styles.moduledrawers import RoundedModuleDrawer, SquareModuleDrawer
    from qrcode.image.styles.colorfills import SolidFillColorMask
    QR_AVAILABLE = True
except ImportError:
    QR_AVAILABLE = False
    logger.warning("QR code library not available. Install qrcode[pil] for QR code support")

try:
    from barcode import Code128, Code39, EAN13, EAN8, UPCA
    from barcode.writer import ImageWriter, SVGWriter
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False
    logger.warning("Barcode library not available. Install python-barcode[images] for barcode support")


class BarcodeType(Enum):
    """Supported barcode types."""
    CODE128 = "code128"
    CODE39 = "code39"
    EAN13 = "ean13"
    EAN8 = "ean8"
    UPCA = "upca"


class QRCodeStyle(Enum):
    """QR code styles."""
    SQUARE = "square"
    ROUNDED = "rounded"
    CIRCLE = "circle"


@dataclass
class BarcodeOptions:
    """Barcode generation options."""
    width: int = 2
    height: int 15
    quiet_zone: int = 6
    font_size: int = 10
    text_distance: int = 5
    background: str = "white"
    foreground: str = "black"
    center_text: bool = True
    write_text: bool = True


@dataclass
class QRCodeOptions:
    """QR code generation options."""
    version: int = 1
    error_correction: str = "M"  # L, M, Q, H
    box_size: int = 10
    border: int = 4
    fill_color: str = "black"
    back_color: str = "white"
    style: QRCodeStyle = QRCodeStyle.SQUARE


@dataclass
class GeneratedCode:
    """Generated barcode/QR code result."""
    code_type: str
    data: str
    image_data: bytes
    image_format: str
    base64_data: str
    file_path: Optional[str] = None
    dimensions: Optional[Tuple[int, int]] = None


class BarcodeGenerator:
    """Barcode and QR code generator."""
    
    def __init__(self, output_dir: str = "generated_codes"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Default options
        self.default_barcode_options = BarcodeOptions()
        self.default_qr_options = QRCodeOptions()
        
        # Supported formats
        self.image_formats = ["PNG", "JPEG", "SVG"]
    
    def generate_product_barcode(self, product_code: str, 
                               barcode_type: BarcodeType = BarcodeType.CODE128,
                               options: Optional[BarcodeOptions] = None,
                               save_file: bool = True) -> GeneratedCode:
        """Generate barcode for product."""
        try:
            if not BARCODE_AVAILABLE:
                raise ImportError("Barcode library not available")
            
            # Use default options if not provided
            if options is None:
                options = self.default_barcode_options
            
            # Generate barcode
            barcode_class = self._get_barcode_class(barcode_type)
            
            # Create barcode with image writer
            writer = ImageWriter()
            writer.set_options({
                'module_width': options.width / 10,
                'module_height': options.height,
                'quiet_zone': options.quiet_zone,
                'font_size': options.font_size,
                'text_distance': options.text_distance,
                'background': options.background,
                'foreground': options.foreground,
                'center_text': options.center_text,
                'write_text': options.write_text
            })
            
            barcode = barcode_class(product_code, writer=writer)
            
            # Generate image
            buffer = io.BytesIO()
            barcode.write(buffer)
            image_data = buffer.getvalue()
            
            # Convert to base64
            base64_data = base64.b64encode(image_data).decode('utf-8')
            
            # Save file if requested
            file_path = None
            if save_file:
                filename = f"barcode_{product_code}_{barcode_type.value}.png"
                file_path = self.output_dir / filename
                with open(file_path, 'wb') as f:
                    f.write(image_data)
            
            return GeneratedCode(
                code_type=f"barcode_{barcode_type.value}",
                data=product_code,
                image_data=image_data,
                image_format="PNG",
                base64_data=base64_data,
                file_path=str(file_path) if file_path else None
            )
            
        except Exception as e:
            logger.error(f"Failed to generate barcode: {e}")
            raise
    
    def generate_qr_code(self, data: str, 
                        options: Optional[QRCodeOptions] = None,
                        save_file: bool = True) -> GeneratedCode:
        """Generate QR code."""
        try:
            if not QR_AVAILABLE:
                raise ImportError("QR code library not available")
            
            # Use default options if not provided
            if options is None:
                options = self.default_qr_options
            
            # Create QR code
            error_correct_map = {
                "L": qrcode.constants.ERROR_CORRECT_L,
                "M": qrcode.constants.ERROR_CORRECT_M,
                "Q": qrcode.constants.ERROR_CORRECT_Q,
                "H": qrcode.constants.ERROR_CORRECT_H
            }
            
            qr = qrcode.QRCode(
                version=options.version,
                error_correction=error_correct_map.get(options.error_correction, qrcode.constants.ERROR_CORRECT_M),
                box_size=options.box_size,
                border=options.border
            )
            
            qr.add_data(data)
            qr.make(fit=True)
            
            # Create image with style
            if options.style == QRCodeStyle.ROUNDED:
                img = qr.make_image(
                    image_factory=StyledPilImage,
                    module_drawer=RoundedModuleDrawer(),
                    fill_color=options.fill_color,
                    back_color=options.back_color
                )
            else:
                img = qr.make_image(
                    fill_color=options.fill_color,
                    back_color=options.back_color
                )
            
            # Convert to bytes
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            image_data = buffer.getvalue()
            
            # Convert to base64
            base64_data = base64.b64encode(image_data).decode('utf-8')
            
            # Save file if requested
            file_path = None
            if save_file:
                # Create safe filename from data
                safe_data = "".join(c for c in data if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_data = safe_data[:50]  # Limit length
                filename = f"qr_{safe_data}_{options.style.value}.png"
                file_path = self.output_dir / filename
                with open(file_path, 'wb') as f:
                    f.write(image_data)
            
            return GeneratedCode(
                code_type="qr_code",
                data=data,
                image_data=image_data,
                image_format="PNG",
                base64_data=base64_data,
                file_path=str(file_path) if file_path else None,
                dimensions=img.size
            )
            
        except Exception as e:
            logger.error(f"Failed to generate QR code: {e}")
            raise
    
    def generate_invoice_qr(self, invoice_data: Dict[str, Any]) -> GeneratedCode:
        """Generate QR code for invoice (ZATCA compliant)."""
        try:
            # Create QR data in ZATCA format
            qr_data = self._create_zatca_qr_data(invoice_data)
            
            # Generate QR code with specific options for invoices
            options = QRCodeOptions(
                version=1,
                error_correction="M",
                box_size=8,
                border=2,
                fill_color="black",
                back_color="white"
            )
            
            return self.generate_qr_code(qr_data, options, save_file=True)
            
        except Exception as e:
            logger.error(f"Failed to generate invoice QR: {e}")
            raise
    
    def generate_batch_barcodes(self, products: List[Dict[str, Any]],
                              barcode_type: BarcodeType = BarcodeType.CODE128) -> List[GeneratedCode]:
        """Generate barcodes for multiple products."""
        try:
            results = []
            
            for product in products:
                product_code = product.get("code") or product.get("id")
                if not product_code:
                    logger.warning(f"No code found for product: {product}")
                    continue
                
                try:
                    barcode = self.generate_product_barcode(
                        product_code=str(product_code),
                        barcode_type=barcode_type,
                        save_file=True
                    )
                    results.append(barcode)
                    
                except Exception as e:
                    logger.error(f"Failed to generate barcode for {product_code}: {e}")
            
            logger.info(f"Generated {len(results)} barcodes out of {len(products)} products")
            return results
            
        except Exception as e:
            logger.error(f"Batch barcode generation failed: {e}")
            return []
    
    def validate_barcode_data(self, data: str, barcode_type: BarcodeType) -> Dict[str, Any]:
        """Validate barcode data for specific type."""
        try:
            validation_result = {
                "valid": False,
                "errors": [],
                "warnings": [],
                "formatted_data": data
            }
            
            if barcode_type == BarcodeType.EAN13:
                # EAN13 must be 12 or 13 digits
                if not data.isdigit():
                    validation_result["errors"].append("EAN13 must contain only digits")
                elif len(data) == 12:
                    # Calculate check digit
                    check_digit = self._calculate_ean13_check_digit(data)
                    validation_result["formatted_data"] = data + str(check_digit)
                    validation_result["valid"] = True
                elif len(data) == 13:
                    # Validate check digit
                    if self._validate_ean13_check_digit(data):
                        validation_result["valid"] = True
                    else:
                        validation_result["errors"].append("Invalid EAN13 check digit")
                else:
                    validation_result["errors"].append("EAN13 must be 12 or 13 digits")
            
            elif barcode_type == BarcodeType.EAN8:
                # EAN8 must be 7 or 8 digits
                if not data.isdigit():
                    validation_result["errors"].append("EAN8 must contain only digits")
                elif len(data) == 7:
                    # Calculate check digit
                    check_digit = self._calculate_ean8_check_digit(data)
                    validation_result["formatted_data"] = data + str(check_digit)
                    validation_result["valid"] = True
                elif len(data) == 8:
                    # Validate check digit
                    if self._validate_ean8_check_digit(data):
                        validation_result["valid"] = True
                    else:
                        validation_result["errors"].append("Invalid EAN8 check digit")
                else:
                    validation_result["errors"].append("EAN8 must be 7 or 8 digits")
            
            elif barcode_type == BarcodeType.CODE128:
                # CODE128 can contain any ASCII character
                if len(data) == 0:
                    validation_result["errors"].append("CODE128 cannot be empty")
                elif len(data) > 80:
                    validation_result["warnings"].append("CODE128 data is very long, may affect readability")
                    validation_result["valid"] = True
                else:
                    validation_result["valid"] = True
            
            elif barcode_type == BarcodeType.CODE39:
                # CODE39 supports limited character set
                valid_chars = set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%")
                if not all(c in valid_chars for c in data.upper()):
                    validation_result["errors"].append("CODE39 contains invalid characters")
                else:
                    validation_result["formatted_data"] = data.upper()
                    validation_result["valid"] = True
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Barcode validation failed: {e}")
            return {"valid": False, "errors": [str(e)], "warnings": [], "formatted_data": data}
    
    def _get_barcode_class(self, barcode_type: BarcodeType):
        """Get barcode class for type."""
        barcode_classes = {
            BarcodeType.CODE128: Code128,
            BarcodeType.CODE39: Code39,
            BarcodeType.EAN13: EAN13,
            BarcodeType.EAN8: EAN8,
            BarcodeType.UPCA: UPCA
        }
        
        return barcode_classes.get(barcode_type, Code128)
    
    def _create_zatca_qr_data(self, invoice_data: Dict[str, Any]) -> str:
        """Create ZATCA-compliant QR data for invoice."""
        try:
            # ZATCA QR code format (simplified)
            qr_parts = [
                f"Seller: {invoice_data.get('company_name', '')}",
                f"VAT: {invoice_data.get('company_vat', '')}",
                f"Date: {invoice_data.get('invoice_date', '')}",
                f"Total: {invoice_data.get('total_amount', '')}",
                f"Tax: {invoice_data.get('tax_amount', '')}"
            ]
            
            return "\n".join(qr_parts)
            
        except Exception as e:
            logger.error(f"Failed to create ZATCA QR data: {e}")
            return str(invoice_data)
    
    def _calculate_ean13_check_digit(self, data: str) -> int:
        """Calculate EAN13 check digit."""
        total = 0
        for i, digit in enumerate(data):
            if i % 2 == 0:
                total += int(digit)
            else:
                total += int(digit) * 3
        
        return (10 - (total % 10)) % 10
    
    def _validate_ean13_check_digit(self, data: str) -> bool:
        """Validate EAN13 check digit."""
        if len(data) != 13:
            return False
        
        calculated_check = self._calculate_ean13_check_digit(data[:-1])
        return calculated_check == int(data[-1])
    
    def _calculate_ean8_check_digit(self, data: str) -> int:
        """Calculate EAN8 check digit."""
        total = 0
        for i, digit in enumerate(data):
            if i % 2 == 0:
                total += int(digit) * 3
            else:
                total += int(digit)
        
        return (10 - (total % 10)) % 10
    
    def _validate_ean8_check_digit(self, data: str) -> bool:
        """Validate EAN8 check digit."""
        if len(data) != 8:
            return False
        
        calculated_check = self._calculate_ean8_check_digit(data[:-1])
        return calculated_check == int(data[-1])
    
    def get_supported_barcode_types(self) -> List[str]:
        """Get list of supported barcode types."""
        return [bt.value for bt in BarcodeType]
    
    def get_supported_qr_styles(self) -> List[str]:
        """Get list of supported QR code styles."""
        return [style.value for style in QRCodeStyle]
    
    def cleanup_old_files(self, days_old: int = 30):
        """Clean up old generated files."""
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days_old * 24 * 60 * 60)
            
            deleted_count = 0
            for file_path in self.output_dir.iterdir():
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1
            
            logger.info(f"Cleaned up {deleted_count} old barcode files")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old files: {e}")

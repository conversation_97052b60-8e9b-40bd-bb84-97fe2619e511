#!/usr/bin/env python3
"""
Dims ERP - Main application entry point.

نظام ERP شامل لإدارة قطع غيار السيارات
"""

import argparse
import asyncio
import sys
from pathlib import Path

import flet as ft
from loguru import logger

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from config import setup_logging, get_settings
from ui.app import DimsERPApp


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Dims ERP - نظام إدارة قطع غيار السيارات",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # تشغيل عادي
  python main.py --lang=ar          # تشغيل بالعربية
  python main.py --theme=dark       # تشغيل بالثيم الداكن
  python main.py --dev              # وضع التطوير
  python main.py --port=8080        # تشغيل على منفذ مخصص
        """
    )
    
    parser.add_argument(
        "--lang", "--language",
        choices=["ar", "en"],
        help="لغة التطبيق (ar=عربي, en=English)"
    )
    
    parser.add_argument(
        "--theme",
        choices=["light", "dark", "auto"],
        help="ثيم التطبيق (light=فاتح, dark=داكن, auto=تلقائي)"
    )
    
    parser.add_argument(
        "--dev", "--development",
        action="store_true",
        help="تشغيل في وضع التطوير مع إعادة التحميل التلقائي"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8550,
        help="منفذ الخادم (افتراضي: 8550)"
    )
    
    parser.add_argument(
        "--host",
        default="localhost",
        help="عنوان الخادم (افتراضي: localhost)"
    )
    
    parser.add_argument(
        "--web",
        action="store_true",
        help="تشغيل كتطبيق ويب فقط"
    )
    
    parser.add_argument(
        "--desktop",
        action="store_true",
        help="تشغيل كتطبيق سطح مكتب فقط"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="تفعيل وضع التصحيح"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="مستوى السجلات"
    )
    
    parser.add_argument(
        "--config",
        help="مسار ملف الإعدادات المخصص"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="Dims ERP v0.1.0"
    )
    
    return parser.parse_args()


def setup_environment(args):
    """Setup application environment based on arguments."""
    settings = get_settings()
    
    # Override settings with command line arguments
    if args.lang:
        settings.default_language = args.lang
    
    if args.theme:
        settings.default_theme = args.theme
    
    if args.debug:
        settings.debug = True
    
    if args.dev:
        settings.debug = True
        settings.environment = "development"
    
    if args.log_level:
        settings.log_level = args.log_level
    
    # Setup logging
    setup_logging()
    
    logger.info(f"Starting Dims ERP v{settings.app_version}")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Language: {settings.default_language}")
    logger.info(f"Theme: {settings.default_theme}")
    logger.info(f"Debug: {settings.debug}")
    
    return settings


async def main_async(args):
    """Async main function."""
    settings = setup_environment(args)
    
    try:
        # Initialize the application
        app = DimsERPApp(settings)
        
        # Determine target based on arguments
        if args.desktop:
            target = ft.AppView.FLET_APP
        elif args.web:
            target = ft.AppView.WEB_BROWSER
        else:
            # Default to desktop app, fallback to web
            target = ft.AppView.FLET_APP_WEB
        
        # Run the application
        await ft.app_async(
            target=app.main,
            name="Dims ERP",
            view=target,
            port=args.port,
            host=args.host,
            assets_dir="assets",
            upload_dir="uploads",
            web_renderer="html",  # Use HTML renderer for better RTL support
        )
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise
    finally:
        logger.info("Application shutdown complete")


def main():
    """Main entry point."""
    try:
        args = parse_arguments()
        
        # Run the async main function
        if sys.platform == "win32":
            # Windows specific event loop policy
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        asyncio.run(main_async(args))
        
    except KeyboardInterrupt:
        print("\n👋 وداعاً! Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ خطأ في بدء التطبيق: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

"""
ZATCA (Saudi Tax Authority) integration for e-invoicing compliance.
"""

import json
import base64
import hashlib
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import httpx
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography import x509
from loguru import logger

from ..config import get_settings


@dataclass
class ZATCAInvoice:
    """ZATCA invoice structure."""
    invoice_number: str
    invoice_date: str
    invoice_type: str  # standard, simplified
    supplier_info: Dict[str, Any]
    customer_info: Dict[str, Any]
    line_items: List[Dict[str, Any]]
    tax_totals: Dict[str, Any]
    totals: Dict[str, Any]
    qr_code: Optional[str] = None
    uuid: Optional[str] = None
    hash: Optional[str] = None


@dataclass
class ZATCAResponse:
    """ZATCA API response."""
    success: bool
    invoice_uuid: Optional[str] = None
    qr_code: Optional[str] = None
    validation_results: Optional[Dict[str, Any]] = None
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None


class ZATCAIntegration:
    """ZATCA e-invoicing integration."""
    
    def __init__(self):
        self.settings = get_settings()
        self.environment = self.settings.zatca_environment  # sandbox or production
        
        # API endpoints
        if self.environment == "production":
            self.base_url = "https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal"
        else:
            self.base_url = "https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal"
        
        # Certificate paths
        self.cert_path = self.settings.zatca_certificate_path
        self.private_key_path = self.settings.zatca_private_key_path
        
        # HTTP client
        self.client = httpx.AsyncClient(
            timeout=30.0,
            verify=True
        )
        
        # Invoice types
        self.invoice_types = {
            "standard": "388",      # Standard invoice
            "simplified": "388",    # Simplified invoice
            "credit_note": "381",   # Credit note
            "debit_note": "383"     # Debit note
        }
        
        # Tax categories
        self.tax_categories = {
            "standard": "S",        # Standard rate
            "zero": "Z",           # Zero rate
            "exempt": "E",         # Exempt
            "not_subject": "O"     # Not subject to tax
        }
    
    async def submit_invoice(self, invoice_data: Dict[str, Any]) -> ZATCAResponse:
        """Submit invoice to ZATCA for validation and clearance."""
        try:
            logger.info(f"Submitting invoice {invoice_data.get('number')} to ZATCA")
            
            # Convert to ZATCA format
            zatca_invoice = await self._convert_to_zatca_format(invoice_data)
            
            # Generate invoice hash
            invoice_hash = self._generate_invoice_hash(zatca_invoice)
            zatca_invoice.hash = invoice_hash
            
            # Generate UUID
            zatca_invoice.uuid = str(uuid.uuid4())
            
            # Create XML document
            xml_document = await self._create_xml_document(zatca_invoice)
            
            # Sign the document
            signed_xml = await self._sign_xml_document(xml_document)
            
            # Submit to ZATCA
            if zatca_invoice.invoice_type == "simplified":
                response = await self._submit_simplified_invoice(signed_xml, zatca_invoice)
            else:
                response = await self._submit_standard_invoice(signed_xml, zatca_invoice)
            
            # Generate QR code
            if response.success:
                qr_code = self._generate_qr_code(zatca_invoice, response)
                response.qr_code = qr_code
            
            return response
            
        except Exception as e:
            logger.error(f"ZATCA submission failed: {e}")
            return ZATCAResponse(
                success=False,
                errors=[str(e)]
            )
    
    async def validate_invoice(self, invoice_data: Dict[str, Any]) -> ZATCAResponse:
        """Validate invoice without submitting for clearance."""
        try:
            logger.info(f"Validating invoice {invoice_data.get('number')} with ZATCA")
            
            # Convert to ZATCA format
            zatca_invoice = await self._convert_to_zatca_format(invoice_data)
            
            # Create XML document
            xml_document = await self._create_xml_document(zatca_invoice)
            
            # Validate with ZATCA
            response = await self._validate_xml_document(xml_document)
            
            return response
            
        except Exception as e:
            logger.error(f"ZATCA validation failed: {e}")
            return ZATCAResponse(
                success=False,
                errors=[str(e)]
            )
    
    async def get_invoice_status(self, invoice_uuid: str) -> Dict[str, Any]:
        """Get invoice status from ZATCA."""
        try:
            endpoint = f"{self.base_url}/invoices/{invoice_uuid}/status"
            
            headers = await self._get_auth_headers()
            response = await self.client.get(endpoint, headers=headers)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to get invoice status: {e}")
            return {"error": str(e)}
    
    async def _convert_to_zatca_format(self, invoice_data: Dict[str, Any]) -> ZATCAInvoice:
        """Convert internal invoice format to ZATCA format."""
        # Determine invoice type
        invoice_type = "simplified" if invoice_data.get("is_simplified", False) else "standard"
        
        # Convert supplier info
        supplier_info = {
            "vat_number": invoice_data.get("company_vat_number", ""),
            "name": invoice_data.get("company_name", ""),
            "address": {
                "street": invoice_data.get("company_address", ""),
                "city": invoice_data.get("company_city", ""),
                "postal_code": invoice_data.get("company_postal_code", ""),
                "country": "SA"
            }
        }
        
        # Convert customer info
        customer_info = {}
        if invoice_type == "standard":
            customer_info = {
                "vat_number": invoice_data.get("customer_vat_number", ""),
                "name": invoice_data.get("customer_name", ""),
                "address": {
                    "street": invoice_data.get("customer_address", ""),
                    "city": invoice_data.get("customer_city", ""),
                    "postal_code": invoice_data.get("customer_postal_code", ""),
                    "country": "SA"
                }
            }
        
        # Convert line items
        line_items = []
        for item in invoice_data.get("items", []):
            line_items.append({
                "id": str(item.get("line_number", 1)),
                "name": item.get("product_name", ""),
                "quantity": float(item.get("quantity", 0)),
                "unit_price": float(item.get("unit_price", 0)),
                "line_total": float(item.get("line_total", 0)),
                "tax_rate": float(item.get("tax_rate", 0.15)),
                "tax_amount": float(item.get("tax_amount", 0)),
                "total_amount": float(item.get("total_amount", 0)),
                "tax_category": self._get_tax_category(item.get("tax_rate", 0.15))
            })
        
        # Convert tax totals
        tax_totals = {
            "tax_amount": float(invoice_data.get("tax_amount", 0)),
            "taxable_amount": float(invoice_data.get("subtotal", 0)) - float(invoice_data.get("discount_amount", 0))
        }
        
        # Convert totals
        totals = {
            "line_extension_amount": float(invoice_data.get("subtotal", 0)),
            "tax_exclusive_amount": float(invoice_data.get("subtotal", 0)) - float(invoice_data.get("discount_amount", 0)),
            "tax_inclusive_amount": float(invoice_data.get("total_amount", 0)),
            "allowance_total_amount": float(invoice_data.get("discount_amount", 0)),
            "payable_amount": float(invoice_data.get("total_amount", 0))
        }
        
        return ZATCAInvoice(
            invoice_number=invoice_data.get("number", ""),
            invoice_date=invoice_data.get("invoice_date", ""),
            invoice_type=invoice_type,
            supplier_info=supplier_info,
            customer_info=customer_info,
            line_items=line_items,
            tax_totals=tax_totals,
            totals=totals
        )
    
    def _generate_invoice_hash(self, invoice: ZATCAInvoice) -> str:
        """Generate invoice hash for ZATCA."""
        # Create hash input string
        hash_input = (
            f"{invoice.invoice_number}"
            f"{invoice.invoice_date}"
            f"{invoice.totals['payable_amount']}"
            f"{invoice.tax_totals['tax_amount']}"
        )
        
        # Generate SHA-256 hash
        hash_bytes = hashlib.sha256(hash_input.encode('utf-8')).digest()
        return base64.b64encode(hash_bytes).decode('utf-8')
    
    async def _create_xml_document(self, invoice: ZATCAInvoice) -> str:
        """Create UBL XML document for ZATCA."""
        # This is a simplified XML structure
        # In production, use proper UBL 2.1 XML generation
        
        xml_template = f"""<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
    
    <cbc:ID>{invoice.invoice_number}</cbc:ID>
    <cbc:UUID>{invoice.uuid}</cbc:UUID>
    <cbc:IssueDate>{invoice.invoice_date}</cbc:IssueDate>
    <cbc:InvoiceTypeCode>{self.invoice_types.get(invoice.invoice_type, '388')}</cbc:InvoiceTypeCode>
    
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cac:PartyIdentification>
                <cbc:ID schemeID="VAT">{invoice.supplier_info['vat_number']}</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>{invoice.supplier_info['name']}</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>{invoice.supplier_info['address']['street']}</cbc:StreetName>
                <cbc:CityName>{invoice.supplier_info['address']['city']}</cbc:CityName>
                <cbc:PostalZone>{invoice.supplier_info['address']['postal_code']}</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>{invoice.supplier_info['address']['country']}</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
        </cac:Party>
    </cac:AccountingSupplierParty>
    
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="SAR">{invoice.totals['line_extension_amount']}</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="SAR">{invoice.totals['tax_exclusive_amount']}</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="SAR">{invoice.totals['tax_inclusive_amount']}</cbc:TaxInclusiveAmount>
        <cbc:PayableAmount currencyID="SAR">{invoice.totals['payable_amount']}</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
    
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="SAR">{invoice.tax_totals['tax_amount']}</cbc:TaxAmount>
    </cac:TaxTotal>
    
</Invoice>"""
        
        return xml_template
    
    async def _sign_xml_document(self, xml_document: str) -> str:
        """Sign XML document with certificate."""
        try:
            # Load private key
            with open(self.private_key_path, 'rb') as key_file:
                private_key = serialization.load_pem_private_key(
                    key_file.read(),
                    password=None
                )
            
            # Load certificate
            with open(self.cert_path, 'rb') as cert_file:
                certificate = x509.load_pem_x509_certificate(cert_file.read())
            
            # Create signature (simplified)
            signature = private_key.sign(
                xml_document.encode('utf-8'),
                padding.PKCS1v15(),
                hashes.SHA256()
            )
            
            # Encode signature
            signature_b64 = base64.b64encode(signature).decode('utf-8')
            
            # Add signature to XML (simplified)
            signed_xml = xml_document.replace(
                '</Invoice>',
                f'<Signature>{signature_b64}</Signature></Invoice>'
            )
            
            return signed_xml
            
        except Exception as e:
            logger.error(f"Failed to sign XML document: {e}")
            raise
    
    async def _submit_simplified_invoice(self, signed_xml: str, invoice: ZATCAInvoice) -> ZATCAResponse:
        """Submit simplified invoice to ZATCA."""
        try:
            endpoint = f"{self.base_url}/invoices/reporting/single"
            
            headers = await self._get_auth_headers()
            headers['Content-Type'] = 'application/json'
            
            payload = {
                "invoiceHash": invoice.hash,
                "uuid": invoice.uuid,
                "invoice": base64.b64encode(signed_xml.encode('utf-8')).decode('utf-8')
            }
            
            response = await self.client.post(endpoint, json=payload, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            
            return ZATCAResponse(
                success=True,
                invoice_uuid=invoice.uuid,
                validation_results=result.get("validationResults", {}),
                warnings=result.get("warningMessages", [])
            )
            
        except Exception as e:
            logger.error(f"Failed to submit simplified invoice: {e}")
            return ZATCAResponse(
                success=False,
                errors=[str(e)]
            )
    
    async def _submit_standard_invoice(self, signed_xml: str, invoice: ZATCAInvoice) -> ZATCAResponse:
        """Submit standard invoice to ZATCA for clearance."""
        try:
            endpoint = f"{self.base_url}/invoices/clearance/single"
            
            headers = await self._get_auth_headers()
            headers['Content-Type'] = 'application/json'
            
            payload = {
                "invoiceHash": invoice.hash,
                "uuid": invoice.uuid,
                "invoice": base64.b64encode(signed_xml.encode('utf-8')).decode('utf-8')
            }
            
            response = await self.client.post(endpoint, json=payload, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            
            return ZATCAResponse(
                success=True,
                invoice_uuid=invoice.uuid,
                validation_results=result.get("validationResults", {}),
                warnings=result.get("warningMessages", [])
            )
            
        except Exception as e:
            logger.error(f"Failed to submit standard invoice: {e}")
            return ZATCAResponse(
                success=False,
                errors=[str(e)]
            )
    
    async def _validate_xml_document(self, xml_document: str) -> ZATCAResponse:
        """Validate XML document with ZATCA."""
        try:
            endpoint = f"{self.base_url}/invoices/validate"
            
            headers = await self._get_auth_headers()
            headers['Content-Type'] = 'application/json'
            
            payload = {
                "invoice": base64.b64encode(xml_document.encode('utf-8')).decode('utf-8')
            }
            
            response = await self.client.post(endpoint, json=payload, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            
            return ZATCAResponse(
                success=result.get("isValid", False),
                validation_results=result.get("validationResults", {}),
                errors=result.get("errorMessages", []),
                warnings=result.get("warningMessages", [])
            )
            
        except Exception as e:
            logger.error(f"Failed to validate XML document: {e}")
            return ZATCAResponse(
                success=False,
                errors=[str(e)]
            )
    
    def _generate_qr_code(self, invoice: ZATCAInvoice, response: ZATCAResponse) -> str:
        """Generate QR code for invoice."""
        try:
            # QR code data structure for ZATCA
            qr_data = {
                "seller_name": invoice.supplier_info['name'],
                "vat_number": invoice.supplier_info['vat_number'],
                "timestamp": invoice.invoice_date,
                "total_amount": str(invoice.totals['payable_amount']),
                "tax_amount": str(invoice.tax_totals['tax_amount']),
                "invoice_hash": invoice.hash,
                "digital_signature": response.invoice_uuid or ""
            }
            
            # Convert to TLV format (Tag-Length-Value)
            tlv_data = self._create_tlv_data(qr_data)
            
            # Encode as base64
            qr_code = base64.b64encode(tlv_data).decode('utf-8')
            
            return qr_code
            
        except Exception as e:
            logger.error(f"Failed to generate QR code: {e}")
            return ""
    
    def _create_tlv_data(self, qr_data: Dict[str, str]) -> bytes:
        """Create TLV (Tag-Length-Value) data for QR code."""
        tlv_bytes = b""
        
        # Tag mapping for ZATCA QR code
        tags = {
            "seller_name": 1,
            "vat_number": 2,
            "timestamp": 3,
            "total_amount": 4,
            "tax_amount": 5,
            "invoice_hash": 6,
            "digital_signature": 7
        }
        
        for key, value in qr_data.items():
            if key in tags:
                tag = tags[key]
                value_bytes = value.encode('utf-8')
                length = len(value_bytes)
                
                tlv_bytes += bytes([tag, length]) + value_bytes
        
        return tlv_bytes
    
    def _get_tax_category(self, tax_rate: float) -> str:
        """Get tax category code based on tax rate."""
        if tax_rate == 0.15:
            return "S"  # Standard rate
        elif tax_rate == 0.0:
            return "Z"  # Zero rate
        else:
            return "S"  # Default to standard
    
    async def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for ZATCA API."""
        # TODO: Implement proper authentication
        # This would typically involve certificate-based authentication
        return {
            "Accept": "application/json",
            "Accept-Language": "en"
        }
    
    async def close(self):
        """Close HTTP client."""
        await self.client.aclose()

"""
WhatsApp Business API integration for sending invoices and notifications.
"""

import json
import base64
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import httpx
from loguru import logger

from ..config import get_settings


@dataclass
class WhatsAppMessage:
    """WhatsApp message structure."""
    to: str
    message_type: str  # text, document, image, template
    content: Dict[str, Any]
    template_name: Optional[str] = None
    template_language: str = "ar"


@dataclass
class WhatsAppContact:
    """WhatsApp contact information."""
    phone_number: str
    name: str
    is_business: bool = False
    profile_name: Optional[str] = None


class WhatsAppIntegration:
    """WhatsApp Business API integration."""
    
    def __init__(self):
        self.settings = get_settings()
        self.phone_number_id = self.settings.whatsapp_phone_number_id
        self.access_token = self.settings.whatsapp_access_token
        self.business_account_id = self.settings.whatsapp_business_account_id
        
        # API endpoints
        self.base_url = "https://graph.facebook.com/v18.0"
        self.messages_url = f"{self.base_url}/{self.phone_number_id}/messages"
        self.media_url = f"{self.base_url}/{self.phone_number_id}/media"
        
        # HTTP client
        self.client = httpx.AsyncClient(
            headers={
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            },
            timeout=30.0
        )
        
        # Message templates
        self.templates = {
            "invoice_notification": {
                "name": "invoice_notification",
                "language": {"code": "ar"},
                "components": [
                    {
                        "type": "header",
                        "parameters": [
                            {"type": "text", "text": "{{company_name}}"}
                        ]
                    },
                    {
                        "type": "body",
                        "parameters": [
                            {"type": "text", "text": "{{customer_name}}"},
                            {"type": "text", "text": "{{invoice_number}}"},
                            {"type": "text", "text": "{{total_amount}}"}
                        ]
                    }
                ]
            },
            "payment_reminder": {
                "name": "payment_reminder",
                "language": {"code": "ar"},
                "components": [
                    {
                        "type": "body",
                        "parameters": [
                            {"type": "text", "text": "{{customer_name}}"},
                            {"type": "text", "text": "{{invoice_number}}"},
                            {"type": "text", "text": "{{due_date}}"},
                            {"type": "text", "text": "{{amount_due}}"}
                        ]
                    }
                ]
            },
            "order_confirmation": {
                "name": "order_confirmation",
                "language": {"code": "ar"},
                "components": [
                    {
                        "type": "body",
                        "parameters": [
                            {"type": "text", "text": "{{customer_name}}"},
                            {"type": "text", "text": "{{order_number}}"},
                            {"type": "text", "text": "{{delivery_date}}"}
                        ]
                    }
                ]
            }
        }
    
    async def send_text_message(self, phone_number: str, message: str) -> Dict[str, Any]:
        """Send a simple text message."""
        try:
            # Format phone number
            formatted_phone = self._format_phone_number(phone_number)
            
            payload = {
                "messaging_product": "whatsapp",
                "to": formatted_phone,
                "type": "text",
                "text": {"body": message}
            }
            
            response = await self.client.post(self.messages_url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Text message sent to {formatted_phone}")
            
            return {
                "success": True,
                "message_id": result.get("messages", [{}])[0].get("id"),
                "phone_number": formatted_phone
            }
            
        except Exception as e:
            logger.error(f"Failed to send text message: {e}")
            return {"success": False, "error": str(e)}
    
    async def send_document(self, phone_number: str, document_path: str, 
                          caption: str = "", filename: str = None) -> Dict[str, Any]:
        """Send a document (PDF, Excel, etc.)."""
        try:
            # Upload document first
            media_result = await self._upload_media(document_path, "document")
            if not media_result["success"]:
                return media_result
            
            # Format phone number
            formatted_phone = self._format_phone_number(phone_number)
            
            # Prepare document message
            document_payload = {
                "id": media_result["media_id"]
            }
            
            if caption:
                document_payload["caption"] = caption
            
            if filename:
                document_payload["filename"] = filename
            
            payload = {
                "messaging_product": "whatsapp",
                "to": formatted_phone,
                "type": "document",
                "document": document_payload
            }
            
            response = await self.client.post(self.messages_url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Document sent to {formatted_phone}")
            
            return {
                "success": True,
                "message_id": result.get("messages", [{}])[0].get("id"),
                "phone_number": formatted_phone,
                "media_id": media_result["media_id"]
            }
            
        except Exception as e:
            logger.error(f"Failed to send document: {e}")
            return {"success": False, "error": str(e)}
    
    async def send_invoice(self, phone_number: str, invoice_data: Dict[str, Any], 
                         pdf_path: str) -> Dict[str, Any]:
        """Send invoice notification with PDF attachment."""
        try:
            # Send invoice document
            document_result = await self.send_document(
                phone_number=phone_number,
                document_path=pdf_path,
                caption=f"فاتورة رقم {invoice_data['number']} - {invoice_data['total_amount']}",
                filename=f"invoice_{invoice_data['number']}.pdf"
            )
            
            if not document_result["success"]:
                return document_result
            
            # Send follow-up text message with details
            message_text = self._format_invoice_message(invoice_data)
            text_result = await self.send_text_message(phone_number, message_text)
            
            return {
                "success": True,
                "document_message_id": document_result["message_id"],
                "text_message_id": text_result.get("message_id"),
                "phone_number": phone_number
            }
            
        except Exception as e:
            logger.error(f"Failed to send invoice: {e}")
            return {"success": False, "error": str(e)}
    
    async def send_template_message(self, phone_number: str, template_name: str, 
                                  parameters: Dict[str, str]) -> Dict[str, Any]:
        """Send a template message."""
        try:
            template = self.templates.get(template_name)
            if not template:
                return {"success": False, "error": f"Template {template_name} not found"}
            
            # Format phone number
            formatted_phone = self._format_phone_number(phone_number)
            
            # Build template components with parameters
            components = []
            for component in template["components"]:
                if component["type"] in ["header", "body", "footer"]:
                    component_params = []
                    for param in component.get("parameters", []):
                        param_key = param["text"].replace("{{", "").replace("}}", "")
                        if param_key in parameters:
                            component_params.append({
                                "type": "text",
                                "text": parameters[param_key]
                            })
                    
                    if component_params:
                        components.append({
                            "type": component["type"],
                            "parameters": component_params
                        })
            
            payload = {
                "messaging_product": "whatsapp",
                "to": formatted_phone,
                "type": "template",
                "template": {
                    "name": template["name"],
                    "language": template["language"],
                    "components": components
                }
            }
            
            response = await self.client.post(self.messages_url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Template message sent to {formatted_phone}")
            
            return {
                "success": True,
                "message_id": result.get("messages", [{}])[0].get("id"),
                "phone_number": formatted_phone,
                "template": template_name
            }
            
        except Exception as e:
            logger.error(f"Failed to send template message: {e}")
            return {"success": False, "error": str(e)}
    
    async def send_payment_reminder(self, phone_number: str, invoice_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send payment reminder for overdue invoice."""
        try:
            parameters = {
                "customer_name": invoice_data.get("customer_name", "عزيزنا العميل"),
                "invoice_number": invoice_data.get("number", ""),
                "due_date": invoice_data.get("due_date", ""),
                "amount_due": invoice_data.get("balance_due", "")
            }
            
            return await self.send_template_message(
                phone_number, "payment_reminder", parameters
            )
            
        except Exception as e:
            logger.error(f"Failed to send payment reminder: {e}")
            return {"success": False, "error": str(e)}
    
    async def send_order_confirmation(self, phone_number: str, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send order confirmation message."""
        try:
            parameters = {
                "customer_name": order_data.get("customer_name", "عزيزنا العميل"),
                "order_number": order_data.get("number", ""),
                "delivery_date": order_data.get("delivery_date", "قريباً")
            }
            
            return await self.send_template_message(
                phone_number, "order_confirmation", parameters
            )
            
        except Exception as e:
            logger.error(f"Failed to send order confirmation: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_message_status(self, message_id: str) -> Dict[str, Any]:
        """Get message delivery status."""
        try:
            # WhatsApp doesn't provide direct message status API
            # Status is typically received via webhooks
            return {
                "success": True,
                "message_id": message_id,
                "status": "sent"  # Placeholder
            }
            
        except Exception as e:
            logger.error(f"Failed to get message status: {e}")
            return {"success": False, "error": str(e)}
    
    async def _upload_media(self, file_path: str, media_type: str) -> Dict[str, Any]:
        """Upload media file to WhatsApp."""
        try:
            with open(file_path, 'rb') as file:
                files = {
                    'file': (file_path.split('/')[-1], file, self._get_mime_type(file_path)),
                    'type': (None, media_type),
                    'messaging_product': (None, 'whatsapp')
                }
                
                # Remove Content-Type header for multipart upload
                headers = {"Authorization": f"Bearer {self.access_token}"}
                
                async with httpx.AsyncClient(headers=headers, timeout=60.0) as client:
                    response = await client.post(self.media_url, files=files)
                    response.raise_for_status()
                    
                    result = response.json()
                    
                    return {
                        "success": True,
                        "media_id": result.get("id")
                    }
                    
        except Exception as e:
            logger.error(f"Failed to upload media: {e}")
            return {"success": False, "error": str(e)}
    
    def _format_phone_number(self, phone_number: str) -> str:
        """Format phone number for WhatsApp API."""
        # Remove all non-digit characters
        digits_only = ''.join(filter(str.isdigit, phone_number))
        
        # Add country code if not present
        if not digits_only.startswith('966'):
            if digits_only.startswith('0'):
                digits_only = '966' + digits_only[1:]
            else:
                digits_only = '966' + digits_only
        
        return digits_only
    
    def _get_mime_type(self, file_path: str) -> str:
        """Get MIME type for file."""
        extension = file_path.lower().split('.')[-1]
        mime_types = {
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif'
        }
        
        return mime_types.get(extension, 'application/octet-stream')
    
    def _format_invoice_message(self, invoice_data: Dict[str, Any]) -> str:
        """Format invoice details message."""
        message = f"""
🧾 *فاتورة جديدة*

📋 رقم الفاتورة: {invoice_data.get('number', '')}
📅 التاريخ: {invoice_data.get('invoice_date', '')}
💰 المبلغ الإجمالي: {invoice_data.get('total_amount', '')}
📋 حالة الدفع: {invoice_data.get('payment_status', '')}

شكراً لتعاملكم معنا 🙏
        """.strip()
        
        return message
    
    async def verify_webhook(self, verify_token: str, challenge: str) -> Optional[str]:
        """Verify webhook for WhatsApp."""
        if verify_token == self.settings.whatsapp_webhook_verify_token:
            return challenge
        return None
    
    async def process_webhook(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming webhook from WhatsApp."""
        try:
            # Extract message data
            entry = webhook_data.get("entry", [{}])[0]
            changes = entry.get("changes", [{}])[0]
            value = changes.get("value", {})
            
            # Process messages
            messages = value.get("messages", [])
            for message in messages:
                await self._process_incoming_message(message)
            
            # Process status updates
            statuses = value.get("statuses", [])
            for status in statuses:
                await self._process_status_update(status)
            
            return {"success": True}
            
        except Exception as e:
            logger.error(f"Failed to process webhook: {e}")
            return {"success": False, "error": str(e)}
    
    async def _process_incoming_message(self, message: Dict[str, Any]):
        """Process incoming message from customer."""
        try:
            message_id = message.get("id")
            from_number = message.get("from")
            message_type = message.get("type")
            timestamp = message.get("timestamp")
            
            logger.info(f"Received {message_type} message from {from_number}")
            
            # TODO: Implement message processing logic
            # - Auto-reply for common queries
            # - Forward to customer service
            # - Extract order/invoice references
            
        except Exception as e:
            logger.error(f"Failed to process incoming message: {e}")
    
    async def _process_status_update(self, status: Dict[str, Any]):
        """Process message status update."""
        try:
            message_id = status.get("id")
            status_type = status.get("status")  # sent, delivered, read, failed
            timestamp = status.get("timestamp")
            
            logger.info(f"Message {message_id} status: {status_type}")
            
            # TODO: Update message status in database
            
        except Exception as e:
            logger.error(f"Failed to process status update: {e}")
    
    async def close(self):
        """Close HTTP client."""
        await self.client.aclose()

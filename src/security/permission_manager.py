"""
Advanced permission and role management system.
"""

from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.orm import Session
from loguru import logger

from ..data.models import User, Role, Permission


class PermissionType(Enum):
    """Types of permissions."""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    EXECUTE = "execute"
    ADMIN = "admin"


class ResourceType(Enum):
    """Types of resources that can have permissions."""
    DASHBOARD = "dashboard"
    SALES = "sales"
    PURCHASES = "purchases"
    INVENTORY = "inventory"
    CUSTOMERS = "customers"
    SUPPLIERS = "suppliers"
    PRODUCTS = "products"
    INVOICES = "invoices"
    PAYMENTS = "payments"
    REPORTS = "reports"
    SETTINGS = "settings"
    USERS = "users"
    ROLES = "roles"
    AUDIT = "audit"
    BACKUP = "backup"
    INTEGRATIONS = "integrations"
    MARKETPLACE = "marketplace"
    SHARING = "sharing"


@dataclass
class PermissionRule:
    """Permission rule structure."""
    resource: ResourceType
    permission: PermissionType
    conditions: Dict[str, Any] = None
    description: str = ""


@dataclass
class RoleDefinition:
    """Role definition with permissions."""
    name: str
    description: str
    permissions: List[PermissionRule]
    is_system_role: bool = False
    level: int = 0


class PermissionManager:
    """Advanced permission and role management system."""
    
    def __init__(self, session: Session):
        self.session = session
        
        # Define system roles
        self.system_roles = {
            "super_admin": RoleDefinition(
                name="Super Administrator",
                description="Full system access with all permissions",
                permissions=self._get_super_admin_permissions(),
                is_system_role=True,
                level=100
            ),
            "admin": RoleDefinition(
                name="Administrator",
                description="Company administrator with most permissions",
                permissions=self._get_admin_permissions(),
                is_system_role=True,
                level=90
            ),
            "manager": RoleDefinition(
                name="Manager",
                description="Department manager with supervisory permissions",
                permissions=self._get_manager_permissions(),
                is_system_role=True,
                level=70
            ),
            "sales_rep": RoleDefinition(
                name="Sales Representative",
                description="Sales team member with sales-focused permissions",
                permissions=self._get_sales_rep_permissions(),
                is_system_role=True,
                level=50
            ),
            "inventory_clerk": RoleDefinition(
                name="Inventory Clerk",
                description="Inventory management with limited permissions",
                permissions=self._get_inventory_clerk_permissions(),
                is_system_role=True,
                level=40
            ),
            "cashier": RoleDefinition(
                name="Cashier",
                description="Point of sale operations only",
                permissions=self._get_cashier_permissions(),
                is_system_role=True,
                level=30
            ),
            "viewer": RoleDefinition(
                name="Viewer",
                description="Read-only access to most data",
                permissions=self._get_viewer_permissions(),
                is_system_role=True,
                level=10
            )
        }
    
    async def check_permission(self, user_id: str, resource: ResourceType, 
                             permission: PermissionType, 
                             context: Dict[str, Any] = None) -> bool:
        """Check if user has specific permission for resource."""
        try:
            # Get user with roles
            user = self.session.query(User).filter(User.id == user_id).first()
            if not user or not user.is_active:
                return False
            
            # Super admin always has access
            if self._is_super_admin(user):
                return True
            
            # Check user's roles
            user_permissions = await self._get_user_permissions(user)
            
            # Check if user has the required permission
            for perm_rule in user_permissions:
                if (perm_rule.resource == resource and 
                    perm_rule.permission == permission):
                    
                    # Check conditions if any
                    if perm_rule.conditions:
                        if not self._check_conditions(perm_rule.conditions, context or {}):
                            continue
                    
                    return True
            
            # Check for admin permission (admin can do anything)
            for perm_rule in user_permissions:
                if (perm_rule.resource == resource and 
                    perm_rule.permission == PermissionType.ADMIN):
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Permission check failed: {e}")
            return False
    
    async def get_user_permissions(self, user_id: str) -> List[PermissionRule]:
        """Get all permissions for a user."""
        try:
            user = self.session.query(User).filter(User.id == user_id).first()
            if not user:
                return []
            
            return await self._get_user_permissions(user)
            
        except Exception as e:
            logger.error(f"Failed to get user permissions: {e}")
            return []
    
    async def assign_role(self, user_id: str, role_name: str, 
                         assigned_by: str) -> bool:
        """Assign role to user."""
        try:
            # Get user
            user = self.session.query(User).filter(User.id == user_id).first()
            if not user:
                logger.error(f"User {user_id} not found")
                return False
            
            # Get role
            role = self.session.query(Role).filter(
                Role.name == role_name,
                Role.company_id == user.company_id
            ).first()
            
            if not role:
                # Create role if it's a system role
                if role_name in self.system_roles:
                    role = await self._create_system_role(role_name, user.company_id)
                else:
                    logger.error(f"Role {role_name} not found")
                    return False
            
            # Check if user already has this role
            if role in user.roles:
                logger.warning(f"User {user_id} already has role {role_name}")
                return True
            
            # Assign role
            user.roles.append(role)
            self.session.commit()
            
            logger.info(f"Role {role_name} assigned to user {user_id} by {assigned_by}")
            return True
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to assign role: {e}")
            return False
    
    async def remove_role(self, user_id: str, role_name: str, 
                         removed_by: str) -> bool:
        """Remove role from user."""
        try:
            # Get user
            user = self.session.query(User).filter(User.id == user_id).first()
            if not user:
                return False
            
            # Get role
            role = self.session.query(Role).filter(
                Role.name == role_name,
                Role.company_id == user.company_id
            ).first()
            
            if not role or role not in user.roles:
                return False
            
            # Remove role
            user.roles.remove(role)
            self.session.commit()
            
            logger.info(f"Role {role_name} removed from user {user_id} by {removed_by}")
            return True
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to remove role: {e}")
            return False
    
    async def create_custom_role(self, company_id: str, role_name: str, 
                               description: str, permissions: List[PermissionRule],
                               created_by: str) -> bool:
        """Create custom role for company."""
        try:
            # Check if role already exists
            existing_role = self.session.query(Role).filter(
                Role.name == role_name,
                Role.company_id == company_id
            ).first()
            
            if existing_role:
                logger.error(f"Role {role_name} already exists")
                return False
            
            # Create role
            role = Role(
                name=role_name,
                description=description,
                company_id=company_id,
                is_system_role=False,
                created_by=created_by
            )
            
            self.session.add(role)
            self.session.flush()  # Get role ID
            
            # Add permissions
            for perm_rule in permissions:
                permission = Permission(
                    role_id=role.id,
                    resource=perm_rule.resource.value,
                    permission=perm_rule.permission.value,
                    conditions=perm_rule.conditions,
                    description=perm_rule.description
                )
                self.session.add(permission)
            
            self.session.commit()
            
            logger.info(f"Custom role {role_name} created for company {company_id}")
            return True
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to create custom role: {e}")
            return False
    
    async def get_available_roles(self, company_id: str) -> List[Dict[str, Any]]:
        """Get all available roles for company."""
        try:
            # Get company-specific roles
            company_roles = self.session.query(Role).filter(
                Role.company_id == company_id
            ).all()
            
            roles = []
            
            # Add system roles
            for role_key, role_def in self.system_roles.items():
                roles.append({
                    "name": role_key,
                    "display_name": role_def.name,
                    "description": role_def.description,
                    "is_system_role": True,
                    "level": role_def.level,
                    "permission_count": len(role_def.permissions)
                })
            
            # Add custom roles
            for role in company_roles:
                if not role.is_system_role:
                    permission_count = self.session.query(Permission).filter(
                        Permission.role_id == role.id
                    ).count()
                    
                    roles.append({
                        "name": role.name,
                        "display_name": role.name,
                        "description": role.description,
                        "is_system_role": False,
                        "level": 0,
                        "permission_count": permission_count
                    })
            
            return sorted(roles, key=lambda x: x["level"], reverse=True)
            
        except Exception as e:
            logger.error(f"Failed to get available roles: {e}")
            return []
    
    async def get_role_permissions(self, role_name: str, 
                                 company_id: str) -> List[PermissionRule]:
        """Get permissions for a specific role."""
        try:
            # Check if it's a system role
            if role_name in self.system_roles:
                return self.system_roles[role_name].permissions
            
            # Get custom role
            role = self.session.query(Role).filter(
                Role.name == role_name,
                Role.company_id == company_id
            ).first()
            
            if not role:
                return []
            
            # Get permissions
            permissions = self.session.query(Permission).filter(
                Permission.role_id == role.id
            ).all()
            
            permission_rules = []
            for perm in permissions:
                permission_rules.append(PermissionRule(
                    resource=ResourceType(perm.resource),
                    permission=PermissionType(perm.permission),
                    conditions=perm.conditions,
                    description=perm.description
                ))
            
            return permission_rules
            
        except Exception as e:
            logger.error(f"Failed to get role permissions: {e}")
            return []
    
    def _is_super_admin(self, user: User) -> bool:
        """Check if user is super admin."""
        return any(role.name == "super_admin" for role in user.roles)
    
    async def _get_user_permissions(self, user: User) -> List[PermissionRule]:
        """Get all permissions for user from their roles."""
        all_permissions = []
        
        for role in user.roles:
            if role.is_system_role and role.name in self.system_roles:
                # System role
                role_permissions = self.system_roles[role.name].permissions
            else:
                # Custom role
                role_permissions = await self.get_role_permissions(role.name, user.company_id)
            
            all_permissions.extend(role_permissions)
        
        # Remove duplicates
        unique_permissions = []
        seen = set()
        
        for perm in all_permissions:
            key = (perm.resource, perm.permission)
            if key not in seen:
                seen.add(key)
                unique_permissions.append(perm)
        
        return unique_permissions
    
    def _check_conditions(self, conditions: Dict[str, Any], 
                         context: Dict[str, Any]) -> bool:
        """Check if permission conditions are met."""
        try:
            for condition_key, condition_value in conditions.items():
                context_value = context.get(condition_key)
                
                if condition_key == "owner_only":
                    # User can only access their own records
                    if condition_value and context.get("owner_id") != context.get("user_id"):
                        return False
                
                elif condition_key == "branch_only":
                    # User can only access records from their branch
                    if condition_value and context.get("branch_id") != context.get("user_branch_id"):
                        return False
                
                elif condition_key == "max_amount":
                    # User can only access records below certain amount
                    record_amount = context.get("amount", 0)
                    if record_amount > condition_value:
                        return False
                
                elif condition_key == "time_restriction":
                    # Time-based access restrictions
                    # TODO: Implement time-based checks
                    pass
            
            return True
            
        except Exception as e:
            logger.error(f"Condition check failed: {e}")
            return False
    
    async def _create_system_role(self, role_name: str, company_id: str) -> Role:
        """Create system role for company."""
        role_def = self.system_roles[role_name]
        
        role = Role(
            name=role_name,
            description=role_def.description,
            company_id=company_id,
            is_system_role=True
        )
        
        self.session.add(role)
        self.session.flush()
        
        # Add permissions
        for perm_rule in role_def.permissions:
            permission = Permission(
                role_id=role.id,
                resource=perm_rule.resource.value,
                permission=perm_rule.permission.value,
                conditions=perm_rule.conditions,
                description=perm_rule.description
            )
            self.session.add(permission)
        
        return role
    
    # System role permission definitions
    def _get_super_admin_permissions(self) -> List[PermissionRule]:
        """Get super admin permissions (all permissions)."""
        permissions = []
        for resource in ResourceType:
            for permission in PermissionType:
                permissions.append(PermissionRule(
                    resource=resource,
                    permission=permission,
                    description=f"Super admin {permission.value} access to {resource.value}"
                ))
        return permissions
    
    def _get_admin_permissions(self) -> List[PermissionRule]:
        """Get admin permissions."""
        permissions = []
        
        # Full access to most resources
        full_access_resources = [
            ResourceType.DASHBOARD, ResourceType.SALES, ResourceType.PURCHASES,
            ResourceType.INVENTORY, ResourceType.CUSTOMERS, ResourceType.SUPPLIERS,
            ResourceType.PRODUCTS, ResourceType.INVOICES, ResourceType.PAYMENTS,
            ResourceType.REPORTS, ResourceType.MARKETPLACE, ResourceType.SHARING
        ]
        
        for resource in full_access_resources:
            for permission in [PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE]:
                permissions.append(PermissionRule(resource=resource, permission=permission))
        
        # Limited access to system resources
        permissions.extend([
            PermissionRule(ResourceType.SETTINGS, PermissionType.READ),
            PermissionRule(ResourceType.SETTINGS, PermissionType.WRITE),
            PermissionRule(ResourceType.USERS, PermissionType.READ),
            PermissionRule(ResourceType.USERS, PermissionType.WRITE),
            PermissionRule(ResourceType.ROLES, PermissionType.READ),
            PermissionRule(ResourceType.AUDIT, PermissionType.READ),
            PermissionRule(ResourceType.INTEGRATIONS, PermissionType.READ),
            PermissionRule(ResourceType.INTEGRATIONS, PermissionType.WRITE)
        ])
        
        return permissions
    
    def _get_manager_permissions(self) -> List[PermissionRule]:
        """Get manager permissions."""
        permissions = []
        
        # Read/Write access to operational resources
        operational_resources = [
            ResourceType.DASHBOARD, ResourceType.SALES, ResourceType.PURCHASES,
            ResourceType.INVENTORY, ResourceType.CUSTOMERS, ResourceType.SUPPLIERS,
            ResourceType.PRODUCTS, ResourceType.INVOICES, ResourceType.PAYMENTS
        ]
        
        for resource in operational_resources:
            permissions.extend([
                PermissionRule(resource=resource, permission=PermissionType.READ),
                PermissionRule(resource=resource, permission=PermissionType.WRITE)
            ])
        
        # Read access to reports and limited delete
        permissions.extend([
            PermissionRule(ResourceType.REPORTS, PermissionType.READ),
            PermissionRule(ResourceType.REPORTS, PermissionType.EXECUTE),
            PermissionRule(ResourceType.SALES, PermissionType.DELETE, 
                         conditions={"max_amount": 10000}),
            PermissionRule(ResourceType.MARKETPLACE, PermissionType.READ),
            PermissionRule(ResourceType.MARKETPLACE, PermissionType.WRITE)
        ])
        
        return permissions
    
    def _get_sales_rep_permissions(self) -> List[PermissionRule]:
        """Get sales representative permissions."""
        return [
            PermissionRule(ResourceType.DASHBOARD, PermissionType.READ),
            PermissionRule(ResourceType.SALES, PermissionType.READ),
            PermissionRule(ResourceType.SALES, PermissionType.WRITE),
            PermissionRule(ResourceType.CUSTOMERS, PermissionType.READ),
            PermissionRule(ResourceType.CUSTOMERS, PermissionType.WRITE),
            PermissionRule(ResourceType.PRODUCTS, PermissionType.READ),
            PermissionRule(ResourceType.INVOICES, PermissionType.READ),
            PermissionRule(ResourceType.INVOICES, PermissionType.WRITE),
            PermissionRule(ResourceType.PAYMENTS, PermissionType.READ),
            PermissionRule(ResourceType.INVENTORY, PermissionType.READ),
            PermissionRule(ResourceType.MARKETPLACE, PermissionType.READ)
        ]
    
    def _get_inventory_clerk_permissions(self) -> List[PermissionRule]:
        """Get inventory clerk permissions."""
        return [
            PermissionRule(ResourceType.DASHBOARD, PermissionType.READ),
            PermissionRule(ResourceType.INVENTORY, PermissionType.READ),
            PermissionRule(ResourceType.INVENTORY, PermissionType.WRITE),
            PermissionRule(ResourceType.PRODUCTS, PermissionType.READ),
            PermissionRule(ResourceType.PRODUCTS, PermissionType.WRITE),
            PermissionRule(ResourceType.PURCHASES, PermissionType.READ),
            PermissionRule(ResourceType.PURCHASES, PermissionType.WRITE),
            PermissionRule(ResourceType.SUPPLIERS, PermissionType.READ),
            PermissionRule(ResourceType.REPORTS, PermissionType.READ)
        ]
    
    def _get_cashier_permissions(self) -> List[PermissionRule]:
        """Get cashier permissions."""
        return [
            PermissionRule(ResourceType.SALES, PermissionType.READ),
            PermissionRule(ResourceType.SALES, PermissionType.WRITE),
            PermissionRule(ResourceType.CUSTOMERS, PermissionType.READ),
            PermissionRule(ResourceType.PRODUCTS, PermissionType.READ),
            PermissionRule(ResourceType.INVOICES, PermissionType.READ),
            PermissionRule(ResourceType.INVOICES, PermissionType.WRITE),
            PermissionRule(ResourceType.PAYMENTS, PermissionType.READ),
            PermissionRule(ResourceType.PAYMENTS, PermissionType.WRITE),
            PermissionRule(ResourceType.INVENTORY, PermissionType.READ)
        ]
    
    def _get_viewer_permissions(self) -> List[PermissionRule]:
        """Get viewer permissions (read-only)."""
        read_only_resources = [
            ResourceType.DASHBOARD, ResourceType.SALES, ResourceType.PURCHASES,
            ResourceType.INVENTORY, ResourceType.CUSTOMERS, ResourceType.SUPPLIERS,
            ResourceType.PRODUCTS, ResourceType.INVOICES, ResourceType.PAYMENTS,
            ResourceType.REPORTS
        ]
        
        return [
            PermissionRule(resource=resource, permission=PermissionType.READ)
            for resource in read_only_resources
        ]

"""
Comprehensive audit logging system.
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc
from loguru import logger

from ..data.models import User, Company


class AuditAction(Enum):
    """Types of audit actions."""
    LOGIN = "login"
    LOGOUT = "logout"
    LOGIN_FAILED = "login_failed"
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    VIEW = "view"
    EXPORT = "export"
    IMPORT = "import"
    PRINT = "print"
    EMAIL = "email"
    BACKUP = "backup"
    RESTORE = "restore"
    SYNC = "sync"
    PAYMENT = "payment"
    REFUND = "refund"
    PERMISSION_CHANGE = "permission_change"
    SETTINGS_CHANGE = "settings_change"
    SECURITY_VIOLATION = "security_violation"


class AuditLevel(Enum):
    """Audit log levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class AuditEntry:
    """Audit log entry structure."""
    id: str
    timestamp: datetime
    user_id: Optional[str]
    user_name: Optional[str]
    company_id: str
    action: AuditAction
    level: AuditLevel
    resource_type: str
    resource_id: Optional[str]
    resource_name: Optional[str]
    description: str
    details: Dict[str, Any]
    ip_address: Optional[str]
    user_agent: Optional[str]
    session_id: Optional[str]
    success: bool
    error_message: Optional[str] = None


class AuditLogger:
    """Comprehensive audit logging system."""
    
    def __init__(self, session: Session):
        self.session = session
        
        # Audit configuration
        self.log_levels = {
            AuditAction.LOGIN: AuditLevel.INFO,
            AuditAction.LOGOUT: AuditLevel.INFO,
            AuditAction.LOGIN_FAILED: AuditLevel.WARNING,
            AuditAction.CREATE: AuditLevel.INFO,
            AuditAction.UPDATE: AuditLevel.INFO,
            AuditAction.DELETE: AuditLevel.WARNING,
            AuditAction.PERMISSION_CHANGE: AuditLevel.WARNING,
            AuditAction.SETTINGS_CHANGE: AuditLevel.WARNING,
            AuditAction.SECURITY_VIOLATION: AuditLevel.CRITICAL,
            AuditAction.PAYMENT: AuditLevel.INFO,
            AuditAction.REFUND: AuditLevel.WARNING
        }
        
        # Sensitive fields to mask in logs
        self.sensitive_fields = {
            "password", "token", "secret", "key", "pin", "ssn", 
            "credit_card", "bank_account", "api_key"
        }
    
    async def log_action(self, action: AuditAction, user_id: Optional[str], 
                        company_id: str, resource_type: str,
                        resource_id: Optional[str] = None,
                        resource_name: Optional[str] = None,
                        description: str = "",
                        details: Dict[str, Any] = None,
                        ip_address: Optional[str] = None,
                        user_agent: Optional[str] = None,
                        session_id: Optional[str] = None,
                        success: bool = True,
                        error_message: Optional[str] = None) -> str:
        """Log an audit action."""
        try:
            # Get user information
            user_name = None
            if user_id:
                user = self.session.query(User).filter(User.id == user_id).first()
                if user:
                    user_name = user.username
            
            # Determine audit level
            level = self.log_levels.get(action, AuditLevel.INFO)
            if not success:
                level = AuditLevel.ERROR
            
            # Clean sensitive data from details
            cleaned_details = self._clean_sensitive_data(details or {})
            
            # Create audit entry
            audit_entry = AuditEntry(
                id=f"audit_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}",
                timestamp=datetime.utcnow(),
                user_id=user_id,
                user_name=user_name,
                company_id=company_id,
                action=action,
                level=level,
                resource_type=resource_type,
                resource_id=resource_id,
                resource_name=resource_name,
                description=description,
                details=cleaned_details,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id,
                success=success,
                error_message=error_message
            )
            
            # Store in database
            await self._store_audit_entry(audit_entry)
            
            # Log to file system
            self._log_to_file(audit_entry)
            
            # Check for security violations
            if level == AuditLevel.CRITICAL:
                await self._handle_security_violation(audit_entry)
            
            return audit_entry.id
            
        except Exception as e:
            logger.error(f"Failed to log audit action: {e}")
            return ""
    
    async def log_login(self, user_id: str, company_id: str, success: bool,
                       ip_address: str = None, user_agent: str = None,
                       session_id: str = None, error_message: str = None) -> str:
        """Log user login attempt."""
        action = AuditAction.LOGIN if success else AuditAction.LOGIN_FAILED
        
        details = {
            "login_time": datetime.utcnow().isoformat(),
            "ip_address": ip_address,
            "user_agent": user_agent
        }
        
        if not success and error_message:
            details["error"] = error_message
        
        return await self.log_action(
            action=action,
            user_id=user_id,
            company_id=company_id,
            resource_type="authentication",
            description=f"User {'login' if success else 'login failed'}",
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            success=success,
            error_message=error_message
        )
    
    async def log_logout(self, user_id: str, company_id: str,
                        session_id: str = None) -> str:
        """Log user logout."""
        details = {
            "logout_time": datetime.utcnow().isoformat(),
            "session_duration": "calculated_duration"  # TODO: Calculate actual duration
        }
        
        return await self.log_action(
            action=AuditAction.LOGOUT,
            user_id=user_id,
            company_id=company_id,
            resource_type="authentication",
            description="User logout",
            details=details,
            session_id=session_id
        )
    
    async def log_data_change(self, action: AuditAction, user_id: str, 
                             company_id: str, resource_type: str,
                             resource_id: str, resource_name: str = None,
                             old_values: Dict[str, Any] = None,
                             new_values: Dict[str, Any] = None,
                             ip_address: str = None) -> str:
        """Log data creation, update, or deletion."""
        details = {}
        
        if action == AuditAction.CREATE and new_values:
            details["new_values"] = self._clean_sensitive_data(new_values)
            description = f"Created {resource_type}"
        elif action == AuditAction.UPDATE and (old_values or new_values):
            if old_values:
                details["old_values"] = self._clean_sensitive_data(old_values)
            if new_values:
                details["new_values"] = self._clean_sensitive_data(new_values)
            details["changes"] = self._get_field_changes(old_values or {}, new_values or {})
            description = f"Updated {resource_type}"
        elif action == AuditAction.DELETE and old_values:
            details["deleted_values"] = self._clean_sensitive_data(old_values)
            description = f"Deleted {resource_type}"
        else:
            description = f"{action.value.title()} {resource_type}"
        
        return await self.log_action(
            action=action,
            user_id=user_id,
            company_id=company_id,
            resource_type=resource_type,
            resource_id=resource_id,
            resource_name=resource_name,
            description=description,
            details=details,
            ip_address=ip_address
        )
    
    async def log_financial_transaction(self, action: AuditAction, user_id: str,
                                      company_id: str, transaction_type: str,
                                      transaction_id: str, amount: float,
                                      currency: str = "SAR",
                                      details: Dict[str, Any] = None) -> str:
        """Log financial transactions."""
        transaction_details = {
            "transaction_type": transaction_type,
            "amount": amount,
            "currency": currency,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if details:
            transaction_details.update(self._clean_sensitive_data(details))
        
        return await self.log_action(
            action=action,
            user_id=user_id,
            company_id=company_id,
            resource_type="financial_transaction",
            resource_id=transaction_id,
            description=f"{action.value.title()} {transaction_type} - {amount} {currency}",
            details=transaction_details
        )
    
    async def log_security_violation(self, user_id: Optional[str], company_id: str,
                                   violation_type: str, description: str,
                                   details: Dict[str, Any] = None,
                                   ip_address: str = None) -> str:
        """Log security violations."""
        violation_details = {
            "violation_type": violation_type,
            "severity": "high",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if details:
            violation_details.update(details)
        
        return await self.log_action(
            action=AuditAction.SECURITY_VIOLATION,
            user_id=user_id,
            company_id=company_id,
            resource_type="security",
            description=f"Security violation: {description}",
            details=violation_details,
            ip_address=ip_address,
            success=False
        )
    
    async def get_audit_logs(self, company_id: str, 
                           filters: Dict[str, Any] = None,
                           limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Retrieve audit logs with filters."""
        try:
            # TODO: Implement database query for audit logs
            # This would query the audit_logs table with filters
            
            # Placeholder implementation
            logs = []
            
            return logs
            
        except Exception as e:
            logger.error(f"Failed to retrieve audit logs: {e}")
            return []
    
    async def get_user_activity(self, user_id: str, company_id: str,
                              date_from: datetime = None,
                              date_to: datetime = None) -> Dict[str, Any]:
        """Get user activity summary."""
        try:
            # TODO: Implement user activity analysis
            
            activity_summary = {
                "user_id": user_id,
                "total_actions": 0,
                "login_count": 0,
                "last_login": None,
                "actions_by_type": {},
                "actions_by_day": {},
                "security_violations": 0
            }
            
            return activity_summary
            
        except Exception as e:
            logger.error(f"Failed to get user activity: {e}")
            return {}
    
    async def get_security_summary(self, company_id: str,
                                 date_from: datetime = None,
                                 date_to: datetime = None) -> Dict[str, Any]:
        """Get security summary for company."""
        try:
            # TODO: Implement security summary analysis
            
            security_summary = {
                "total_violations": 0,
                "failed_logins": 0,
                "suspicious_activities": 0,
                "data_breaches": 0,
                "violations_by_type": {},
                "violations_by_user": {},
                "risk_level": "low"
            }
            
            return security_summary
            
        except Exception as e:
            logger.error(f"Failed to get security summary: {e}")
            return {}
    
    def _clean_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Remove or mask sensitive data from log details."""
        cleaned_data = {}
        
        for key, value in data.items():
            key_lower = key.lower()
            
            # Check if field is sensitive
            is_sensitive = any(sensitive in key_lower for sensitive in self.sensitive_fields)
            
            if is_sensitive:
                if isinstance(value, str) and len(value) > 4:
                    # Mask all but last 4 characters
                    cleaned_data[key] = "*" * (len(value) - 4) + value[-4:]
                else:
                    cleaned_data[key] = "***MASKED***"
            elif isinstance(value, dict):
                cleaned_data[key] = self._clean_sensitive_data(value)
            elif isinstance(value, list):
                cleaned_data[key] = [
                    self._clean_sensitive_data(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                cleaned_data[key] = value
        
        return cleaned_data
    
    def _get_field_changes(self, old_values: Dict[str, Any], 
                          new_values: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get list of field changes between old and new values."""
        changes = []
        
        # Check for modified fields
        for key, new_value in new_values.items():
            old_value = old_values.get(key)
            if old_value != new_value:
                changes.append({
                    "field": key,
                    "old_value": old_value,
                    "new_value": new_value,
                    "change_type": "modified"
                })
        
        # Check for removed fields
        for key, old_value in old_values.items():
            if key not in new_values:
                changes.append({
                    "field": key,
                    "old_value": old_value,
                    "new_value": None,
                    "change_type": "removed"
                })
        
        return changes
    
    async def _store_audit_entry(self, entry: AuditEntry):
        """Store audit entry in database."""
        try:
            # TODO: Implement database storage
            # This would insert the audit entry into audit_logs table
            pass
            
        except Exception as e:
            logger.error(f"Failed to store audit entry: {e}")
            raise
    
    def _log_to_file(self, entry: AuditEntry):
        """Log audit entry to file system."""
        try:
            # Create structured log entry
            log_data = {
                "timestamp": entry.timestamp.isoformat(),
                "level": entry.level.value,
                "action": entry.action.value,
                "user": entry.user_name or entry.user_id,
                "company": entry.company_id,
                "resource": f"{entry.resource_type}:{entry.resource_id}",
                "description": entry.description,
                "success": entry.success,
                "ip": entry.ip_address,
                "details": entry.details
            }
            
            # Log based on level
            if entry.level == AuditLevel.CRITICAL:
                logger.critical(f"AUDIT: {json.dumps(log_data)}")
            elif entry.level == AuditLevel.ERROR:
                logger.error(f"AUDIT: {json.dumps(log_data)}")
            elif entry.level == AuditLevel.WARNING:
                logger.warning(f"AUDIT: {json.dumps(log_data)}")
            else:
                logger.info(f"AUDIT: {json.dumps(log_data)}")
                
        except Exception as e:
            logger.error(f"Failed to log to file: {e}")
    
    async def _handle_security_violation(self, entry: AuditEntry):
        """Handle critical security violations."""
        try:
            # TODO: Implement security violation handling
            # - Send alerts to administrators
            # - Lock user account if necessary
            # - Trigger additional security measures
            
            logger.critical(f"SECURITY VIOLATION: {entry.description}")
            
        except Exception as e:
            logger.error(f"Failed to handle security violation: {e}")
    
    async def export_audit_logs(self, company_id: str, 
                              date_from: datetime, date_to: datetime,
                              format: str = "csv") -> str:
        """Export audit logs for compliance."""
        try:
            # TODO: Implement audit log export
            # This would generate a file with audit logs for the specified period
            
            export_path = f"exports/audit_logs_{company_id}_{date_from.strftime('%Y%m%d')}_{date_to.strftime('%Y%m%d')}.{format}"
            
            return export_path
            
        except Exception as e:
            logger.error(f"Failed to export audit logs: {e}")
            return ""

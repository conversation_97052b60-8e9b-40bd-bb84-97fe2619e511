"""
OEM Manager for handling OEM numbers and cross-references.
"""

import re
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass
from sqlalchemy.orm import Session
from loguru import logger

from ..data.models import Product, ProductOEM
from .oem_database import OEMDatabase
from .vehicle_matcher import VehicleMatcher


@dataclass
class OEMMatch:
    """Represents an OEM number match."""
    oem_number: str
    manufacturer: str
    confidence: float
    match_type: str  # exact, partial, similar, cross_reference
    product_id: Optional[str] = None
    vehicle_info: Optional[Dict[str, Any]] = None


@dataclass
class OEMSearchResult:
    """OEM search result."""
    query: str
    matches: List[OEMMatch]
    suggestions: List[str]
    total_found: int
    search_time: float


class OEMManager:
    """Advanced OEM number management system."""
    
    def __init__(self, session: Session):
        self.session = session
        self.oem_database = OEMDatabase()
        self.vehicle_matcher = VehicleMatcher()
        
        # OEM number patterns and cleaning rules
        self.cleaning_patterns = [
            (r'[^\w\-]', ''),  # Remove special characters except hyphens
            (r'\s+', ''),      # Remove spaces
            (r'\-+', '-'),     # Normalize multiple hyphens
        ]
        
        # Common OEM number formats
        self.oem_formats = {
            'mercedes': r'^[A-Z0-9]{3,4}[\-\s]?[A-Z0-9]{3,6}[\-\s]?[A-Z0-9]{2,4}$',
            'bmw': r'^[0-9]{2,3}[\-\s]?[0-9]{2}[\-\s]?[0-9][\-\s]?[0-9]{3}[\-\s]?[0-9]{3}$',
            'audi': r'^[0-9A-Z]{3}[\-\s]?[0-9]{3}[\-\s]?[0-9A-Z]{3}[\-\s]?[A-Z]?$',
            'toyota': r'^[0-9]{5}[\-\s]?[0-9A-Z]{2,5}[\-\s]?[0-9]{3}$',
            'honda': r'^[0-9]{5}[\-\s]?[A-Z]{2,3}[\-\s]?[A-Z0-9]{3}$',
            'nissan': r'^[0-9]{5}[\-\s]?[A-Z0-9]{2,5}$',
            'hyundai': r'^[0-9]{5}[\-\s]?[A-Z0-9]{2,5}$',
            'kia': r'^[0-9]{5}[\-\s]?[A-Z0-9]{2,5}$',
            'generic': r'^[A-Z0-9\-]{5,20}$'
        }
    
    async def search_oem(self, query: str, limit: int = 50) -> OEMSearchResult:
        """Search for OEM numbers with advanced matching."""
        start_time = time.time()
        
        try:
            # Clean and normalize query
            cleaned_query = self._clean_oem_number(query)
            
            # Perform different types of searches
            matches = []
            
            # 1. Exact match
            exact_matches = await self._search_exact(cleaned_query)
            matches.extend(exact_matches)
            
            # 2. Partial match
            if len(matches) < limit:
                partial_matches = await self._search_partial(cleaned_query, limit - len(matches))
                matches.extend(partial_matches)
            
            # 3. Similar match (fuzzy)
            if len(matches) < limit:
                similar_matches = await self._search_similar(cleaned_query, limit - len(matches))
                matches.extend(similar_matches)
            
            # 4. Cross-reference search
            if len(matches) < limit:
                cross_ref_matches = await self._search_cross_references(cleaned_query, limit - len(matches))
                matches.extend(cross_ref_matches)
            
            # Remove duplicates and sort by confidence
            unique_matches = self._deduplicate_matches(matches)
            unique_matches.sort(key=lambda x: x.confidence, reverse=True)
            
            # Generate suggestions
            suggestions = await self._generate_suggestions(cleaned_query, unique_matches)
            
            search_time = time.time() - start_time
            
            return OEMSearchResult(
                query=query,
                matches=unique_matches[:limit],
                suggestions=suggestions,
                total_found=len(unique_matches),
                search_time=search_time
            )
            
        except Exception as e:
            logger.error(f"OEM search failed for query '{query}': {e}")
            return OEMSearchResult(
                query=query,
                matches=[],
                suggestions=[],
                total_found=0,
                search_time=time.time() - start_time
            )
    
    async def add_oem_number(self, product_id: str, oem_number: str, 
                           manufacturer: str, is_primary: bool = False,
                           is_original: bool = True) -> bool:
        """Add OEM number to a product."""
        try:
            # Clean OEM number
            cleaned_oem = self._clean_oem_number(oem_number)
            
            # Check if OEM number already exists for this product
            existing = self.session.query(ProductOEM).filter(
                ProductOEM.product_id == product_id,
                ProductOEM.oem_number == cleaned_oem
            ).first()
            
            if existing:
                logger.warning(f"OEM number {cleaned_oem} already exists for product {product_id}")
                return False
            
            # If this is primary, unset other primary OEMs for this product
            if is_primary:
                self.session.query(ProductOEM).filter(
                    ProductOEM.product_id == product_id,
                    ProductOEM.is_primary == True
                ).update({"is_primary": False})
            
            # Create new OEM record
            product_oem = ProductOEM(
                product_id=product_id,
                oem_number=cleaned_oem,
                manufacturer=manufacturer,
                is_primary=is_primary,
                is_original=is_original
            )
            
            self.session.add(product_oem)
            self.session.commit()
            
            logger.info(f"Added OEM number {cleaned_oem} to product {product_id}")
            return True
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to add OEM number: {e}")
            return False
    
    async def find_compatible_parts(self, vehicle_make: str, vehicle_model: str, 
                                  vehicle_year: int, part_category: str = None) -> List[Dict[str, Any]]:
        """Find compatible parts for a specific vehicle."""
        try:
            # Use vehicle matcher to find compatible parts
            compatible_parts = await self.vehicle_matcher.find_compatible_parts(
                vehicle_make, vehicle_model, vehicle_year, part_category
            )
            
            # Enrich with OEM information
            enriched_parts = []
            for part in compatible_parts:
                oem_info = await self._get_product_oem_info(part["product_id"])
                part["oem_numbers"] = oem_info
                enriched_parts.append(part)
            
            return enriched_parts
            
        except Exception as e:
            logger.error(f"Failed to find compatible parts: {e}")
            return []
    
    async def get_cross_references(self, oem_number: str) -> List[Dict[str, Any]]:
        """Get cross-references for an OEM number."""
        try:
            cleaned_oem = self._clean_oem_number(oem_number)
            
            # Search in OEM database
            cross_refs = await self.oem_database.get_cross_references(cleaned_oem)
            
            # Search in local database
            local_refs = await self._get_local_cross_references(cleaned_oem)
            
            # Combine and deduplicate
            all_refs = cross_refs + local_refs
            unique_refs = self._deduplicate_cross_references(all_refs)
            
            return unique_refs
            
        except Exception as e:
            logger.error(f"Failed to get cross-references for {oem_number}: {e}")
            return []
    
    async def validate_oem_number(self, oem_number: str, manufacturer: str = None) -> Dict[str, Any]:
        """Validate OEM number format and existence."""
        try:
            cleaned_oem = self._clean_oem_number(oem_number)
            
            validation_result = {
                "original": oem_number,
                "cleaned": cleaned_oem,
                "valid_format": False,
                "manufacturer_detected": None,
                "exists_in_database": False,
                "suggestions": []
            }
            
            # Check format
            detected_manufacturer = self._detect_manufacturer(cleaned_oem)
            if detected_manufacturer:
                validation_result["valid_format"] = True
                validation_result["manufacturer_detected"] = detected_manufacturer
            
            # Check existence in database
            exists = await self._check_oem_exists(cleaned_oem)
            validation_result["exists_in_database"] = exists
            
            # Generate suggestions if invalid
            if not validation_result["valid_format"] or not exists:
                suggestions = await self._generate_oem_suggestions(cleaned_oem)
                validation_result["suggestions"] = suggestions
            
            return validation_result
            
        except Exception as e:
            logger.error(f"OEM validation failed for {oem_number}: {e}")
            return {"error": str(e)}
    
    def _clean_oem_number(self, oem_number: str) -> str:
        """Clean and normalize OEM number."""
        if not oem_number:
            return ""
        
        cleaned = oem_number.upper().strip()
        
        # Apply cleaning patterns
        for pattern, replacement in self.cleaning_patterns:
            cleaned = re.sub(pattern, replacement, cleaned)
        
        return cleaned
    
    def _detect_manufacturer(self, oem_number: str) -> Optional[str]:
        """Detect manufacturer from OEM number format."""
        for manufacturer, pattern in self.oem_formats.items():
            if re.match(pattern, oem_number, re.IGNORECASE):
                return manufacturer
        
        return None
    
    async def _search_exact(self, query: str) -> List[OEMMatch]:
        """Search for exact OEM matches."""
        matches = []
        
        try:
            # Search in local database
            local_oems = self.session.query(ProductOEM).filter(
                ProductOEM.oem_number == query
            ).all()
            
            for oem in local_oems:
                matches.append(OEMMatch(
                    oem_number=oem.oem_number,
                    manufacturer=oem.manufacturer,
                    confidence=1.0,
                    match_type="exact",
                    product_id=str(oem.product_id)
                ))
            
            # Search in OEM database
            db_matches = await self.oem_database.search_exact(query)
            for match in db_matches:
                matches.append(OEMMatch(
                    oem_number=match["oem_number"],
                    manufacturer=match["manufacturer"],
                    confidence=1.0,
                    match_type="exact"
                ))
            
        except Exception as e:
            logger.error(f"Exact search failed: {e}")
        
        return matches
    
    async def _search_partial(self, query: str, limit: int) -> List[OEMMatch]:
        """Search for partial OEM matches."""
        matches = []
        
        try:
            # Search in local database with LIKE
            local_oems = self.session.query(ProductOEM).filter(
                ProductOEM.oem_number.like(f"%{query}%")
            ).limit(limit).all()
            
            for oem in local_oems:
                confidence = self._calculate_similarity(query, oem.oem_number)
                matches.append(OEMMatch(
                    oem_number=oem.oem_number,
                    manufacturer=oem.manufacturer,
                    confidence=confidence,
                    match_type="partial",
                    product_id=str(oem.product_id)
                ))
            
        except Exception as e:
            logger.error(f"Partial search failed: {e}")
        
        return matches
    
    async def _search_similar(self, query: str, limit: int) -> List[OEMMatch]:
        """Search for similar OEM numbers using fuzzy matching."""
        matches = []
        
        try:
            # Get all OEM numbers for fuzzy matching
            all_oems = self.session.query(ProductOEM.oem_number, ProductOEM.manufacturer, ProductOEM.product_id).all()
            
            for oem_number, manufacturer, product_id in all_oems:
                similarity = self._calculate_similarity(query, oem_number)
                
                if similarity > 0.6:  # Threshold for similarity
                    matches.append(OEMMatch(
                        oem_number=oem_number,
                        manufacturer=manufacturer,
                        confidence=similarity,
                        match_type="similar",
                        product_id=str(product_id)
                    ))
            
            # Sort by similarity and limit
            matches.sort(key=lambda x: x.confidence, reverse=True)
            matches = matches[:limit]
            
        except Exception as e:
            logger.error(f"Similar search failed: {e}")
        
        return matches
    
    async def _search_cross_references(self, query: str, limit: int) -> List[OEMMatch]:
        """Search for cross-referenced OEM numbers."""
        matches = []
        
        try:
            cross_refs = await self.oem_database.get_cross_references(query)
            
            for ref in cross_refs[:limit]:
                matches.append(OEMMatch(
                    oem_number=ref["oem_number"],
                    manufacturer=ref["manufacturer"],
                    confidence=0.8,  # Cross-reference confidence
                    match_type="cross_reference"
                ))
            
        except Exception as e:
            logger.error(f"Cross-reference search failed: {e}")
        
        return matches
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings."""
        try:
            from difflib import SequenceMatcher
            return SequenceMatcher(None, str1, str2).ratio()
        except:
            # Fallback simple similarity
            if str1 == str2:
                return 1.0
            elif str1 in str2 or str2 in str1:
                return 0.8
            else:
                return 0.0
    
    def _deduplicate_matches(self, matches: List[OEMMatch]) -> List[OEMMatch]:
        """Remove duplicate matches."""
        seen = set()
        unique_matches = []
        
        for match in matches:
            key = (match.oem_number, match.manufacturer)
            if key not in seen:
                seen.add(key)
                unique_matches.append(match)
        
        return unique_matches
    
    def _deduplicate_cross_references(self, refs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate cross-references."""
        seen = set()
        unique_refs = []
        
        for ref in refs:
            key = (ref["oem_number"], ref["manufacturer"])
            if key not in seen:
                seen.add(key)
                unique_refs.append(ref)
        
        return unique_refs
    
    async def _generate_suggestions(self, query: str, matches: List[OEMMatch]) -> List[str]:
        """Generate search suggestions."""
        suggestions = []
        
        try:
            # If no matches, suggest similar OEM numbers
            if not matches:
                similar_oems = await self._get_similar_oem_numbers(query, limit=5)
                suggestions.extend(similar_oems)
            
            # Suggest manufacturer-specific searches
            detected_manufacturer = self._detect_manufacturer(query)
            if detected_manufacturer:
                suggestions.append(f"Search all {detected_manufacturer} parts")
            
        except Exception as e:
            logger.error(f"Failed to generate suggestions: {e}")
        
        return suggestions[:10]  # Limit suggestions
    
    async def _get_product_oem_info(self, product_id: str) -> List[Dict[str, Any]]:
        """Get OEM information for a product."""
        try:
            oems = self.session.query(ProductOEM).filter(
                ProductOEM.product_id == product_id
            ).all()
            
            return [
                {
                    "oem_number": oem.oem_number,
                    "manufacturer": oem.manufacturer,
                    "is_primary": oem.is_primary,
                    "is_original": oem.is_original
                }
                for oem in oems
            ]
            
        except Exception as e:
            logger.error(f"Failed to get OEM info for product {product_id}: {e}")
            return []
    
    async def _get_local_cross_references(self, oem_number: str) -> List[Dict[str, Any]]:
        """Get cross-references from local database."""
        # TODO: Implement local cross-reference logic
        return []
    
    async def _check_oem_exists(self, oem_number: str) -> bool:
        """Check if OEM number exists in database."""
        try:
            exists = self.session.query(ProductOEM).filter(
                ProductOEM.oem_number == oem_number
            ).first() is not None
            
            if not exists:
                exists = await self.oem_database.check_exists(oem_number)
            
            return exists
            
        except Exception as e:
            logger.error(f"Failed to check OEM existence: {e}")
            return False
    
    async def _generate_oem_suggestions(self, oem_number: str) -> List[str]:
        """Generate OEM number suggestions."""
        # TODO: Implement suggestion generation
        return []
    
    async def _get_similar_oem_numbers(self, query: str, limit: int = 5) -> List[str]:
        """Get similar OEM numbers."""
        # TODO: Implement similar OEM number search
        return []


# Import time for performance measurement
import time

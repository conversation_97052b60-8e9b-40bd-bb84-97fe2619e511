"""
OEM database for storing and retrieving OEM cross-references.
"""

import json
import sqlite3
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from loguru import logger


@dataclass
class OEMRecord:
    """OEM database record."""
    oem_number: str
    manufacturer: str
    part_name: str
    part_category: str
    vehicle_make: str
    vehicle_model: str
    vehicle_year_from: int
    vehicle_year_to: int
    cross_references: List[str]
    superseded_by: Optional[str] = None
    notes: Optional[str] = None


class OEMDatabase:
    """OEM database manager for cross-references and vehicle compatibility."""
    
    def __init__(self, db_path: str = "data/oem_database.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        # Load manufacturer mappings
        self.manufacturer_mappings = self._load_manufacturer_mappings()
        
        # Load part category mappings
        self.category_mappings = self._load_category_mappings()
    
    def _init_database(self):
        """Initialize OEM database tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # OEM records table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS oem_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        oem_number TEXT NOT NULL,
                        manufacturer TEXT NOT NULL,
                        part_name TEXT,
                        part_category TEXT,
                        vehicle_make TEXT,
                        vehicle_model TEXT,
                        vehicle_year_from INTEGER,
                        vehicle_year_to INTEGER,
                        superseded_by TEXT,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Cross-references table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS oem_cross_references (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        source_oem TEXT NOT NULL,
                        target_oem TEXT NOT NULL,
                        manufacturer TEXT NOT NULL,
                        confidence REAL DEFAULT 1.0,
                        verified BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Vehicle compatibility table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS vehicle_compatibility (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        oem_number TEXT NOT NULL,
                        vehicle_make TEXT NOT NULL,
                        vehicle_model TEXT NOT NULL,
                        vehicle_year_from INTEGER,
                        vehicle_year_to INTEGER,
                        engine_type TEXT,
                        transmission TEXT,
                        body_type TEXT,
                        verified BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create indexes
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_oem_number ON oem_records(oem_number)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_manufacturer ON oem_records(manufacturer)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_vehicle_make ON oem_records(vehicle_make)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_cross_ref_source ON oem_cross_references(source_oem)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_cross_ref_target ON oem_cross_references(target_oem)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_vehicle_compat ON vehicle_compatibility(oem_number)")
                
                conn.commit()
                logger.info("OEM database initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize OEM database: {e}")
            raise
    
    async def search_exact(self, oem_number: str) -> List[Dict[str, Any]]:
        """Search for exact OEM number matches."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT oem_number, manufacturer, part_name, part_category,
                           vehicle_make, vehicle_model, vehicle_year_from, vehicle_year_to
                    FROM oem_records 
                    WHERE oem_number = ? COLLATE NOCASE
                """, (oem_number,))
                
                results = []
                for row in cursor.fetchall():
                    results.append({
                        "oem_number": row[0],
                        "manufacturer": row[1],
                        "part_name": row[2],
                        "part_category": row[3],
                        "vehicle_make": row[4],
                        "vehicle_model": row[5],
                        "vehicle_year_from": row[6],
                        "vehicle_year_to": row[7]
                    })
                
                return results
                
        except Exception as e:
            logger.error(f"Exact search failed for {oem_number}: {e}")
            return []
    
    async def search_partial(self, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Search for partial OEM number matches."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT oem_number, manufacturer, part_name, part_category,
                           vehicle_make, vehicle_model, vehicle_year_from, vehicle_year_to
                    FROM oem_records 
                    WHERE oem_number LIKE ? COLLATE NOCASE
                    LIMIT ?
                """, (f"%{query}%", limit))
                
                results = []
                for row in cursor.fetchall():
                    results.append({
                        "oem_number": row[0],
                        "manufacturer": row[1],
                        "part_name": row[2],
                        "part_category": row[3],
                        "vehicle_make": row[4],
                        "vehicle_model": row[5],
                        "vehicle_year_from": row[6],
                        "vehicle_year_to": row[7]
                    })
                
                return results
                
        except Exception as e:
            logger.error(f"Partial search failed for {query}: {e}")
            return []
    
    async def get_cross_references(self, oem_number: str) -> List[Dict[str, Any]]:
        """Get cross-references for an OEM number."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get direct cross-references
                cursor.execute("""
                    SELECT cr.target_oem, cr.manufacturer, cr.confidence, cr.verified,
                           or.part_name, or.part_category
                    FROM oem_cross_references cr
                    LEFT JOIN oem_records or ON cr.target_oem = or.oem_number
                    WHERE cr.source_oem = ? COLLATE NOCASE
                    ORDER BY cr.confidence DESC, cr.verified DESC
                """, (oem_number,))
                
                results = []
                for row in cursor.fetchall():
                    results.append({
                        "oem_number": row[0],
                        "manufacturer": row[1],
                        "confidence": row[2],
                        "verified": bool(row[3]),
                        "part_name": row[4],
                        "part_category": row[5]
                    })
                
                # Also get reverse cross-references
                cursor.execute("""
                    SELECT cr.source_oem, cr.manufacturer, cr.confidence, cr.verified,
                           or.part_name, or.part_category
                    FROM oem_cross_references cr
                    LEFT JOIN oem_records or ON cr.source_oem = or.oem_number
                    WHERE cr.target_oem = ? COLLATE NOCASE
                    ORDER BY cr.confidence DESC, cr.verified DESC
                """, (oem_number,))
                
                for row in cursor.fetchall():
                    results.append({
                        "oem_number": row[0],
                        "manufacturer": row[1],
                        "confidence": row[2],
                        "verified": bool(row[3]),
                        "part_name": row[4],
                        "part_category": row[5]
                    })
                
                return results
                
        except Exception as e:
            logger.error(f"Cross-reference search failed for {oem_number}: {e}")
            return []
    
    async def get_vehicle_compatibility(self, oem_number: str) -> List[Dict[str, Any]]:
        """Get vehicle compatibility for an OEM number."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT vehicle_make, vehicle_model, vehicle_year_from, vehicle_year_to,
                           engine_type, transmission, body_type, verified
                    FROM vehicle_compatibility
                    WHERE oem_number = ? COLLATE NOCASE
                    ORDER BY vehicle_make, vehicle_model, vehicle_year_from
                """, (oem_number,))
                
                results = []
                for row in cursor.fetchall():
                    results.append({
                        "vehicle_make": row[0],
                        "vehicle_model": row[1],
                        "vehicle_year_from": row[2],
                        "vehicle_year_to": row[3],
                        "engine_type": row[4],
                        "transmission": row[5],
                        "body_type": row[6],
                        "verified": bool(row[7])
                    })
                
                return results
                
        except Exception as e:
            logger.error(f"Vehicle compatibility search failed for {oem_number}: {e}")
            return []
    
    async def add_oem_record(self, record: OEMRecord) -> bool:
        """Add OEM record to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO oem_records (
                        oem_number, manufacturer, part_name, part_category,
                        vehicle_make, vehicle_model, vehicle_year_from, vehicle_year_to,
                        superseded_by, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record.oem_number, record.manufacturer, record.part_name,
                    record.part_category, record.vehicle_make, record.vehicle_model,
                    record.vehicle_year_from, record.vehicle_year_to,
                    record.superseded_by, record.notes
                ))
                
                # Add cross-references
                for cross_ref in record.cross_references:
                    cursor.execute("""
                        INSERT OR IGNORE INTO oem_cross_references (
                            source_oem, target_oem, manufacturer, confidence
                        ) VALUES (?, ?, ?, ?)
                    """, (record.oem_number, cross_ref, record.manufacturer, 1.0))
                
                conn.commit()
                logger.debug(f"Added OEM record: {record.oem_number}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to add OEM record: {e}")
            return False
    
    async def add_cross_reference(self, source_oem: str, target_oem: str, 
                                manufacturer: str, confidence: float = 1.0,
                                verified: bool = False) -> bool:
        """Add cross-reference between OEM numbers."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO oem_cross_references (
                        source_oem, target_oem, manufacturer, confidence, verified
                    ) VALUES (?, ?, ?, ?, ?)
                """, (source_oem, target_oem, manufacturer, confidence, verified))
                
                conn.commit()
                logger.debug(f"Added cross-reference: {source_oem} -> {target_oem}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to add cross-reference: {e}")
            return False
    
    async def check_exists(self, oem_number: str) -> bool:
        """Check if OEM number exists in database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT 1 FROM oem_records WHERE oem_number = ? COLLATE NOCASE LIMIT 1
                """, (oem_number,))
                
                return cursor.fetchone() is not None
                
        except Exception as e:
            logger.error(f"Failed to check OEM existence: {e}")
            return False
    
    async def import_oem_data(self, data_file: str) -> Dict[str, int]:
        """Import OEM data from file."""
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                raise FileNotFoundError(f"Data file not found: {data_file}")
            
            imported = 0
            errors = 0
            
            with open(data_path, 'r', encoding='utf-8') as f:
                if data_path.suffix.lower() == '.json':
                    data = json.load(f)
                    
                    for item in data:
                        try:
                            record = OEMRecord(
                                oem_number=item.get('oem_number', ''),
                                manufacturer=item.get('manufacturer', ''),
                                part_name=item.get('part_name', ''),
                                part_category=item.get('part_category', ''),
                                vehicle_make=item.get('vehicle_make', ''),
                                vehicle_model=item.get('vehicle_model', ''),
                                vehicle_year_from=item.get('vehicle_year_from', 0),
                                vehicle_year_to=item.get('vehicle_year_to', 0),
                                cross_references=item.get('cross_references', []),
                                superseded_by=item.get('superseded_by'),
                                notes=item.get('notes')
                            )
                            
                            if await self.add_oem_record(record):
                                imported += 1
                            else:
                                errors += 1
                                
                        except Exception as e:
                            logger.error(f"Failed to import record: {e}")
                            errors += 1
            
            logger.info(f"OEM data import completed: {imported} imported, {errors} errors")
            
            return {
                "imported": imported,
                "errors": errors,
                "total": imported + errors
            }
            
        except Exception as e:
            logger.error(f"OEM data import failed: {e}")
            return {"imported": 0, "errors": 0, "total": 0}
    
    def _load_manufacturer_mappings(self) -> Dict[str, str]:
        """Load manufacturer name mappings."""
        return {
            "mercedes": "Mercedes-Benz",
            "mercedes-benz": "Mercedes-Benz",
            "mb": "Mercedes-Benz",
            "bmw": "BMW",
            "audi": "Audi",
            "volkswagen": "Volkswagen",
            "vw": "Volkswagen",
            "toyota": "Toyota",
            "honda": "Honda",
            "nissan": "Nissan",
            "hyundai": "Hyundai",
            "kia": "Kia",
            "ford": "Ford",
            "chevrolet": "Chevrolet",
            "gmc": "GMC",
            "cadillac": "Cadillac",
            "lexus": "Lexus",
            "infiniti": "Infiniti",
            "acura": "Acura"
        }
    
    def _load_category_mappings(self) -> Dict[str, str]:
        """Load part category mappings."""
        return {
            "engine": "Engine",
            "transmission": "Transmission",
            "brake": "Brake System",
            "suspension": "Suspension",
            "electrical": "Electrical",
            "cooling": "Cooling System",
            "fuel": "Fuel System",
            "exhaust": "Exhaust System",
            "body": "Body Parts",
            "interior": "Interior",
            "lighting": "Lighting",
            "filters": "Filters",
            "belts": "Belts & Hoses",
            "bearings": "Bearings",
            "gaskets": "Gaskets & Seals"
        }
    
    def normalize_manufacturer(self, manufacturer: str) -> str:
        """Normalize manufacturer name."""
        if not manufacturer:
            return ""
        
        normalized = manufacturer.lower().strip()
        return self.manufacturer_mappings.get(normalized, manufacturer.title())
    
    def normalize_category(self, category: str) -> str:
        """Normalize part category."""
        if not category:
            return ""
        
        normalized = category.lower().strip()
        return self.category_mappings.get(normalized, category.title())

"""
Application settings and configuration management.
"""

import os
from functools import lru_cache
from pathlib import Path
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings loaded from environment variables and .env file."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # Application Configuration
    app_name: str = Field(default="Dims ERP", description="Application name")
    app_version: str = Field(default="0.1.0", description="Application version")
    debug: bool = Field(default=False, description="Debug mode")
    environment: str = Field(default="development", description="Environment")
    
    # Database Configuration
    local_db_path: str = Field(default="data/dims_erp.db", description="Local SQLite database path")
    postgres_host: str = Field(default="localhost", description="PostgreSQL host")
    postgres_port: int = Field(default=5432, description="PostgreSQL port")
    postgres_db: str = Field(default="dims_erp", description="PostgreSQL database name")
    postgres_user: str = Field(default="dims_user", description="PostgreSQL username")
    postgres_password: str = Field(default="", description="PostgreSQL password")
    database_url: Optional[str] = Field(default=None, description="Complete database URL")
    
    # Redis Configuration
    redis_host: str = Field(default="localhost", description="Redis host")
    redis_port: int = Field(default=6379, description="Redis port")
    redis_db: int = Field(default=0, description="Redis database number")
    redis_password: str = Field(default="", description="Redis password")
    redis_url: Optional[str] = Field(default=None, description="Complete Redis URL")
    
    # Security Configuration
    secret_key: str = Field(default="change-this-secret-key", description="Application secret key")
    jwt_secret_key: str = Field(default="change-this-jwt-secret", description="JWT secret key")
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_expire_minutes: int = Field(default=1440, description="JWT expiration in minutes")
    bcrypt_rounds: int = Field(default=12, description="Bcrypt rounds")
    
    # Application Settings
    default_language: str = Field(default="ar", description="Default language")
    default_theme: str = Field(default="light", description="Default theme")
    supported_languages: str = Field(default="ar,en", description="Supported languages")
    rtl_languages: str = Field(default="ar", description="RTL languages")
    
    # Company Information
    company_name: str = Field(default="شركة الأبعاد لقطع الغيار", description="Company name")
    company_name_en: str = Field(default="Dimensions Auto Parts Company", description="Company name in English")
    company_vat_number: str = Field(default="", description="Company VAT number")
    company_cr_number: str = Field(default="", description="Company CR number")
    company_phone: str = Field(default="", description="Company phone")
    company_email: str = Field(default="", description="Company email")
    company_address: str = Field(default="", description="Company address")
    company_address_en: str = Field(default="", description="Company address in English")
    
    # WhatsApp Configuration
    whatsapp_enabled: bool = Field(default=False, description="Enable WhatsApp integration")
    whatsapp_phone_number_id: str = Field(default="", description="WhatsApp phone number ID")
    whatsapp_access_token: str = Field(default="", description="WhatsApp access token")
    whatsapp_verify_token: str = Field(default="", description="WhatsApp verify token")
    whatsapp_webhook_url: str = Field(default="", description="WhatsApp webhook URL")
    
    # ZATCA Configuration
    zatca_enabled: bool = Field(default=False, description="Enable ZATCA integration")
    zatca_environment: str = Field(default="sandbox", description="ZATCA environment")
    
    # Email Configuration
    email_enabled: bool = Field(default=False, description="Enable email")
    smtp_host: str = Field(default="", description="SMTP host")
    smtp_port: int = Field(default=587, description="SMTP port")
    smtp_username: str = Field(default="", description="SMTP username")
    smtp_password: str = Field(default="", description="SMTP password")
    smtp_use_tls: bool = Field(default=True, description="Use TLS for SMTP")
    email_from: str = Field(default="", description="Email from address")
    email_from_name: str = Field(default="Dims ERP System", description="Email from name")
    
    # File Storage Configuration
    upload_dir: str = Field(default="uploads", description="Upload directory")
    max_file_size: int = Field(default=10485760, description="Max file size in bytes")
    allowed_extensions: str = Field(default="pdf,jpg,jpeg,png,xlsx,xls,csv", description="Allowed file extensions")
    
    # Backup Configuration
    backup_enabled: bool = Field(default=True, description="Enable backups")
    backup_interval_hours: int = Field(default=6, description="Backup interval in hours")
    backup_retention_days: int = Field(default=30, description="Backup retention in days")
    backup_dir: str = Field(default="backups", description="Backup directory")
    
    # P2P Sync Configuration
    p2p_enabled: bool = Field(default=True, description="Enable P2P sync")
    p2p_port: int = Field(default=8765, description="P2P port")
    p2p_discovery_port: int = Field(default=8766, description="P2P discovery port")
    p2p_max_peers: int = Field(default=10, description="Max P2P peers")
    p2p_sync_interval_seconds: int = Field(default=30, description="P2P sync interval")
    
    # Central Sync Configuration
    central_sync_enabled: bool = Field(default=True, description="Enable central sync")
    central_sync_url: str = Field(default="", description="Central sync URL")
    central_sync_api_key: str = Field(default="", description="Central sync API key")
    central_sync_interval_seconds: int = Field(default=300, description="Central sync interval")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", description="Log level")
    log_file: str = Field(default="logs/dims_erp.log", description="Log file path")
    log_max_size: str = Field(default="10MB", description="Log file max size")
    log_backup_count: int = Field(default=5, description="Log backup count")
    log_format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        description="Log format"
    )
    
    # Performance Configuration
    max_workers: int = Field(default=4, description="Max workers")
    cache_ttl_seconds: int = Field(default=3600, description="Cache TTL")
    pagination_default_size: int = Field(default=50, description="Default pagination size")
    pagination_max_size: int = Field(default=1000, description="Max pagination size")
    
    # Feature Flags
    feature_sharing_economy: bool = Field(default=True, description="Enable sharing economy")
    feature_multi_branch: bool = Field(default=True, description="Enable multi-branch")
    feature_advanced_reports: bool = Field(default=True, description="Enable advanced reports")
    feature_mobile_app: bool = Field(default=False, description="Enable mobile app")
    feature_barcode_scanner: bool = Field(default=True, description="Enable barcode scanner")
    
    # Localization
    currency_code: str = Field(default="SAR", description="Currency code")
    currency_symbol: str = Field(default="ر.س", description="Currency symbol")
    date_format: str = Field(default="DD/MM/YYYY", description="Date format")
    time_format: str = Field(default="HH:mm", description="Time format")
    timezone: str = Field(default="Asia/Riyadh", description="Timezone")
    
    @property
    def supported_languages_list(self) -> List[str]:
        """Get supported languages as a list."""
        return [lang.strip() for lang in self.supported_languages.split(",")]
    
    @property
    def rtl_languages_list(self) -> List[str]:
        """Get RTL languages as a list."""
        return [lang.strip() for lang in self.rtl_languages.split(",")]
    
    @property
    def allowed_extensions_list(self) -> List[str]:
        """Get allowed extensions as a list."""
        return [ext.strip() for ext in self.allowed_extensions.split(",")]
    
    @property
    def is_rtl_language(self) -> bool:
        """Check if default language is RTL."""
        return self.default_language in self.rtl_languages_list
    
    def get_database_url(self) -> str:
        """Get the appropriate database URL."""
        if self.database_url:
            return self.database_url
        
        if self.environment == "production":
            return f"postgresql://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
        else:
            # Ensure the data directory exists
            db_path = Path(self.local_db_path)
            db_path.parent.mkdir(parents=True, exist_ok=True)
            return f"sqlite:///{self.local_db_path}"
    
    def get_redis_url(self) -> str:
        """Get the Redis URL."""
        if self.redis_url:
            return self.redis_url
        
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        else:
            return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()

"""
Logging configuration for the application.
"""

import sys
from pathlib import Path
from typing import Dict, Any

from loguru import logger

from .settings import get_settings


def setup_logging() -> None:
    """Setup application logging configuration."""
    settings = get_settings()
    
    # Remove default logger
    logger.remove()
    
    # Ensure log directory exists
    log_file_path = Path(settings.log_file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Console logging configuration
    console_config = {
        "sink": sys.stdout,
        "level": settings.log_level,
        "format": _get_console_format(),
        "colorize": True,
        "backtrace": settings.debug,
        "diagnose": settings.debug,
    }
    
    # File logging configuration
    file_config = {
        "sink": settings.log_file,
        "level": settings.log_level,
        "format": settings.log_format,
        "rotation": settings.log_max_size,
        "retention": f"{settings.log_backup_count} files",
        "compression": "zip",
        "backtrace": True,
        "diagnose": True,
        "encoding": "utf-8",
    }
    
    # Add console handler
    logger.add(**console_config)
    
    # Add file handler
    logger.add(**file_config)
    
    # Add error file handler for errors and above
    error_file_config = file_config.copy()
    error_file_config.update({
        "sink": str(log_file_path.parent / "error.log"),
        "level": "ERROR",
        "filter": lambda record: record["level"].no >= logger.level("ERROR").no,
    })
    logger.add(**error_file_config)
    
    # Configure specific loggers
    _configure_third_party_loggers()
    
    logger.info(f"Logging configured - Level: {settings.log_level}, File: {settings.log_file}")


def _get_console_format() -> str:
    """Get console logging format with colors."""
    return (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )


def _configure_third_party_loggers() -> None:
    """Configure third-party library loggers."""
    import logging
    
    # SQLAlchemy logging
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)
    
    # FastAPI/Uvicorn logging
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    
    # Flet logging
    logging.getLogger("flet").setLevel(logging.WARNING)
    logging.getLogger("flet_core").setLevel(logging.WARNING)
    
    # HTTP libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    
    # Async libraries
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    
    # Redirect standard logging to loguru
    class InterceptHandler(logging.Handler):
        def emit(self, record):
            # Get corresponding Loguru level if it exists
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno
            
            # Find caller from where originated the logged message
            frame, depth = logging.currentframe(), 2
            while frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1
            
            logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())
    
    # Replace standard logging handlers
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)


def get_logger(name: str = None):
    """Get a logger instance for a specific module."""
    if name:
        return logger.bind(name=name)
    return logger


# Logging decorators and utilities
def log_function_call(func):
    """Decorator to log function calls."""
    def wrapper(*args, **kwargs):
        func_name = f"{func.__module__}.{func.__name__}"
        logger.debug(f"Calling {func_name} with args={args}, kwargs={kwargs}")
        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func_name} completed successfully")
            return result
        except Exception as e:
            logger.error(f"{func_name} failed with error: {e}")
            raise
    return wrapper


async def log_async_function_call(func):
    """Decorator to log async function calls."""
    async def wrapper(*args, **kwargs):
        func_name = f"{func.__module__}.{func.__name__}"
        logger.debug(f"Calling async {func_name} with args={args}, kwargs={kwargs}")
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"Async {func_name} completed successfully")
            return result
        except Exception as e:
            logger.error(f"Async {func_name} failed with error: {e}")
            raise
    return wrapper


class LogContext:
    """Context manager for adding context to logs."""
    
    def __init__(self, **context):
        self.context = context
        self.token = None
    
    def __enter__(self):
        self.token = logger.contextualize(**self.context)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.token:
            self.token.__exit__(exc_type, exc_val, exc_tb)


# Performance logging utilities
def log_performance(operation_name: str):
    """Decorator to log operation performance."""
    import time
    
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"Performance: {operation_name} completed in {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Performance: {operation_name} failed after {duration:.3f}s - {e}")
                raise
        return wrapper
    return decorator


async def log_async_performance(operation_name: str):
    """Decorator to log async operation performance."""
    import time
    
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"Performance: {operation_name} completed in {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Performance: {operation_name} failed after {duration:.3f}s - {e}")
                raise
        return wrapper
    return decorator

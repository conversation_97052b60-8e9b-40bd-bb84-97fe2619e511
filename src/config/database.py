"""
Database configuration and connection management.
"""

from typing import As<PERSON><PERSON><PERSON>ator, Generator
from sqlalchemy import create_engine, event
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import <PERSON><PERSON><PERSON>ool

from .settings import get_settings


class DatabaseConfig:
    """Database configuration and session management."""
    
    def __init__(self):
        self.settings = get_settings()
        self._engine = None
        self._async_engine = None
        self._session_factory = None
        self._async_session_factory = None
    
    @property
    def engine(self):
        """Get synchronous database engine."""
        if self._engine is None:
            database_url = self.settings.get_database_url()
            
            if database_url.startswith("sqlite"):
                # SQLite specific configuration
                self._engine = create_engine(
                    database_url,
                    poolclass=StaticPool,
                    connect_args={
                        "check_same_thread": False,
                        "timeout": 20,
                    },
                    echo=self.settings.debug,
                )
                
                # Enable foreign keys for SQLite
                @event.listens_for(self._engine, "connect")
                def set_sqlite_pragma(dbapi_connection, connection_record):
                    cursor = dbapi_connection.cursor()
                    cursor.execute("PRAGMA foreign_keys=ON")
                    cursor.execute("PRAGMA journal_mode=WAL")
                    cursor.execute("PRAGMA synchronous=NORMAL")
                    cursor.execute("PRAGMA cache_size=1000")
                    cursor.execute("PRAGMA temp_store=MEMORY")
                    cursor.close()
            else:
                # PostgreSQL configuration
                self._engine = create_engine(
                    database_url,
                    pool_size=10,
                    max_overflow=20,
                    pool_pre_ping=True,
                    echo=self.settings.debug,
                )
        
        return self._engine
    
    @property
    def async_engine(self):
        """Get asynchronous database engine."""
        if self._async_engine is None:
            database_url = self.settings.get_database_url()
            
            # Convert sync URL to async URL
            if database_url.startswith("sqlite"):
                async_url = database_url.replace("sqlite://", "sqlite+aiosqlite://")
                self._async_engine = create_async_engine(
                    async_url,
                    poolclass=StaticPool,
                    connect_args={"check_same_thread": False},
                    echo=self.settings.debug,
                )
            else:
                async_url = database_url.replace("postgresql://", "postgresql+asyncpg://")
                self._async_engine = create_async_engine(
                    async_url,
                    pool_size=10,
                    max_overflow=20,
                    pool_pre_ping=True,
                    echo=self.settings.debug,
                )
        
        return self._async_engine
    
    @property
    def session_factory(self):
        """Get synchronous session factory."""
        if self._session_factory is None:
            self._session_factory = sessionmaker(
                bind=self.engine,
                autocommit=False,
                autoflush=False,
            )
        return self._session_factory
    
    @property
    def async_session_factory(self):
        """Get asynchronous session factory."""
        if self._async_session_factory is None:
            self._async_session_factory = async_sessionmaker(
                bind=self.async_engine,
                class_=AsyncSession,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False,
            )
        return self._async_session_factory
    
    def get_session(self) -> Generator[Session, None, None]:
        """Get synchronous database session."""
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get asynchronous database session."""
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
    
    def close_connections(self):
        """Close all database connections."""
        if self._engine:
            self._engine.dispose()
        if self._async_engine:
            self._async_engine.dispose()


# Global database configuration instance
db_config = DatabaseConfig()

# Dependency functions for FastAPI
def get_db_session() -> Generator[Session, None, None]:
    """Dependency function to get database session."""
    yield from db_config.get_session()

async def get_async_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Dependency function to get async database session."""
    async for session in db_config.get_async_session():
        yield session

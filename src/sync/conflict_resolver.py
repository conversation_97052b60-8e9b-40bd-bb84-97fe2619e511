"""
Conflict resolution system for synchronization.
"""

from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum
from dataclasses import dataclass
from sqlalchemy.orm import Session
from loguru import logger

from ..config import get_settings


class ConflictResolutionStrategy(Enum):
    """Conflict resolution strategies."""
    MANUAL = "manual"              # Require manual resolution
    LOCAL_WINS = "local_wins"      # Local version always wins
    REMOTE_WINS = "remote_wins"    # Remote version always wins
    TIMESTAMP = "timestamp"        # Newest timestamp wins
    MERGE = "merge"               # Attempt to merge changes
    CUSTOM = "custom"             # Use custom resolution logic


class ConflictType(Enum):
    """Types of conflicts."""
    UPDATE_UPDATE = "update_update"  # Both sides updated same record
    UPDATE_DELETE = "update_delete"  # One updated, other deleted
    DELETE_UPDATE = "delete_update"  # One deleted, other updated
    FIELD_CONFLICT = "field_conflict" # Specific field conflicts


@dataclass
class Conflict:
    """Represents a synchronization conflict."""
    id: str
    table_name: str
    record_id: str
    conflict_type: ConflictType
    local_data: Dict[str, Any]
    remote_data: Dict[str, Any]
    conflicting_fields: List[str]
    timestamp: datetime
    resolved: bool = False
    resolution_strategy: Optional[ConflictResolutionStrategy] = None
    resolved_data: Optional[Dict[str, Any]] = None
    resolved_by: Optional[str] = None
    resolved_at: Optional[datetime] = None


class ConflictResolver:
    """Handles synchronization conflicts."""
    
    def __init__(self, session: Session):
        self.session = session
        self.settings = get_settings()
        
        # Default resolution strategies by table
        self.default_strategies = {
            "products": ConflictResolutionStrategy.TIMESTAMP,
            "customers": ConflictResolutionStrategy.TIMESTAMP,
            "suppliers": ConflictResolutionStrategy.TIMESTAMP,
            "invoices": ConflictResolutionStrategy.MANUAL,
            "users": ConflictResolutionStrategy.MANUAL,
            "companies": ConflictResolutionStrategy.MANUAL,
        }
        
        # Active conflicts
        self.conflicts: Dict[str, Conflict] = {}
    
    async def detect_conflict(self, table_name: str, local_record: Dict[str, Any], 
                            remote_record: Dict[str, Any]) -> Optional[Conflict]:
        """Detect if there's a conflict between local and remote records."""
        try:
            record_id = local_record.get("id") or remote_record.get("id")
            
            # Determine conflict type
            conflict_type = self._determine_conflict_type(local_record, remote_record)
            
            if conflict_type is None:
                return None  # No conflict
            
            # Find conflicting fields
            conflicting_fields = self._find_conflicting_fields(local_record, remote_record)
            
            # Create conflict object
            conflict = Conflict(
                id=f"{table_name}_{record_id}_{datetime.utcnow().timestamp()}",
                table_name=table_name,
                record_id=record_id,
                conflict_type=conflict_type,
                local_data=local_record,
                remote_data=remote_record,
                conflicting_fields=conflicting_fields,
                timestamp=datetime.utcnow()
            )
            
            # Store conflict
            self.conflicts[conflict.id] = conflict
            
            logger.warning(f"Conflict detected: {conflict.id} in {table_name}")
            
            return conflict
            
        except Exception as e:
            logger.error(f"Error detecting conflict: {e}")
            return None
    
    async def resolve_conflict(self, conflict_id: str, 
                             strategy: Optional[ConflictResolutionStrategy] = None,
                             user_id: Optional[str] = None,
                             custom_data: Optional[Dict[str, Any]] = None) -> bool:
        """Resolve a specific conflict."""
        try:
            conflict = self.conflicts.get(conflict_id)
            if not conflict:
                logger.error(f"Conflict not found: {conflict_id}")
                return False
            
            if conflict.resolved:
                logger.warning(f"Conflict already resolved: {conflict_id}")
                return True
            
            # Determine resolution strategy
            resolution_strategy = strategy or self.default_strategies.get(
                conflict.table_name, ConflictResolutionStrategy.MANUAL
            )
            
            # Resolve based on strategy
            resolved_data = await self._apply_resolution_strategy(
                conflict, resolution_strategy, custom_data
            )
            
            if resolved_data is None:
                logger.error(f"Failed to resolve conflict: {conflict_id}")
                return False
            
            # Update conflict
            conflict.resolved = True
            conflict.resolution_strategy = resolution_strategy
            conflict.resolved_data = resolved_data
            conflict.resolved_by = user_id
            conflict.resolved_at = datetime.utcnow()
            
            # Apply resolution to database
            await self._apply_resolution(conflict)
            
            logger.info(f"Conflict resolved: {conflict_id} using {resolution_strategy}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error resolving conflict {conflict_id}: {e}")
            return False
    
    async def resolve_all_conflicts(self) -> Dict[str, bool]:
        """Resolve all pending conflicts using default strategies."""
        results = {}
        
        for conflict_id, conflict in self.conflicts.items():
            if not conflict.resolved:
                success = await self.resolve_conflict(conflict_id)
                results[conflict_id] = success
        
        return results
    
    async def resolve_conflicts_manually(self, resolutions: Dict[str, str]) -> bool:
        """Resolve conflicts with manual user decisions."""
        try:
            success_count = 0
            
            for conflict_id, resolution_choice in resolutions.items():
                conflict = self.conflicts.get(conflict_id)
                if not conflict:
                    continue
                
                # Parse resolution choice
                if resolution_choice == "local":
                    strategy = ConflictResolutionStrategy.LOCAL_WINS
                elif resolution_choice == "remote":
                    strategy = ConflictResolutionStrategy.REMOTE_WINS
                elif resolution_choice.startswith("merge:"):
                    # Custom merge data provided
                    strategy = ConflictResolutionStrategy.CUSTOM
                    # TODO: Parse custom merge data
                else:
                    logger.warning(f"Unknown resolution choice: {resolution_choice}")
                    continue
                
                if await self.resolve_conflict(conflict_id, strategy):
                    success_count += 1
            
            return success_count == len(resolutions)
            
        except Exception as e:
            logger.error(f"Manual conflict resolution failed: {e}")
            return False
    
    def _determine_conflict_type(self, local_record: Dict[str, Any], 
                                remote_record: Dict[str, Any]) -> Optional[ConflictType]:
        """Determine the type of conflict."""
        local_deleted = local_record.get("is_deleted", False)
        remote_deleted = remote_record.get("is_deleted", False)
        
        if local_deleted and not remote_deleted:
            return ConflictType.DELETE_UPDATE
        elif not local_deleted and remote_deleted:
            return ConflictType.UPDATE_DELETE
        elif not local_deleted and not remote_deleted:
            # Check if both were updated
            local_updated = local_record.get("updated_at")
            remote_updated = remote_record.get("updated_at")
            
            if local_updated and remote_updated:
                # Check if there are actual field differences
                if self._has_field_differences(local_record, remote_record):
                    return ConflictType.UPDATE_UPDATE
        
        return None  # No conflict
    
    def _has_field_differences(self, local_record: Dict[str, Any], 
                              remote_record: Dict[str, Any]) -> bool:
        """Check if there are field differences between records."""
        # Exclude metadata fields from comparison
        exclude_fields = {"id", "created_at", "updated_at", "version"}
        
        for key in local_record:
            if key in exclude_fields:
                continue
            
            if key in remote_record:
                if local_record[key] != remote_record[key]:
                    return True
        
        return False
    
    def _find_conflicting_fields(self, local_record: Dict[str, Any], 
                                remote_record: Dict[str, Any]) -> List[str]:
        """Find specific fields that have conflicts."""
        conflicting_fields = []
        exclude_fields = {"id", "created_at", "updated_at", "version"}
        
        for key in local_record:
            if key in exclude_fields:
                continue
            
            if key in remote_record:
                if local_record[key] != remote_record[key]:
                    conflicting_fields.append(key)
        
        return conflicting_fields
    
    async def _apply_resolution_strategy(self, conflict: Conflict, 
                                       strategy: ConflictResolutionStrategy,
                                       custom_data: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """Apply resolution strategy to get resolved data."""
        try:
            if strategy == ConflictResolutionStrategy.LOCAL_WINS:
                return conflict.local_data
            
            elif strategy == ConflictResolutionStrategy.REMOTE_WINS:
                return conflict.remote_data
            
            elif strategy == ConflictResolutionStrategy.TIMESTAMP:
                local_updated = conflict.local_data.get("updated_at")
                remote_updated = conflict.remote_data.get("updated_at")
                
                if not local_updated or not remote_updated:
                    # Fallback to remote if timestamps missing
                    return conflict.remote_data
                
                # Parse timestamps and compare
                try:
                    local_dt = datetime.fromisoformat(local_updated.replace('Z', '+00:00'))
                    remote_dt = datetime.fromisoformat(remote_updated.replace('Z', '+00:00'))
                    
                    return conflict.remote_data if remote_dt > local_dt else conflict.local_data
                except ValueError:
                    # Fallback to remote if parsing fails
                    return conflict.remote_data
            
            elif strategy == ConflictResolutionStrategy.MERGE:
                return await self._merge_records(conflict)
            
            elif strategy == ConflictResolutionStrategy.CUSTOM:
                return custom_data or conflict.remote_data
            
            elif strategy == ConflictResolutionStrategy.MANUAL:
                # Manual resolution required - return None to indicate pending
                return None
            
            else:
                logger.error(f"Unknown resolution strategy: {strategy}")
                return None
                
        except Exception as e:
            logger.error(f"Error applying resolution strategy: {e}")
            return None
    
    async def _merge_records(self, conflict: Conflict) -> Dict[str, Any]:
        """Attempt to merge conflicting records."""
        try:
            merged_data = conflict.local_data.copy()
            
            # Merge non-conflicting fields from remote
            for key, value in conflict.remote_data.items():
                if key not in conflict.conflicting_fields:
                    merged_data[key] = value
            
            # For conflicting fields, use timestamp-based resolution
            for field in conflict.conflicting_fields:
                local_updated = conflict.local_data.get("updated_at")
                remote_updated = conflict.remote_data.get("updated_at")
                
                if remote_updated and local_updated:
                    try:
                        local_dt = datetime.fromisoformat(local_updated.replace('Z', '+00:00'))
                        remote_dt = datetime.fromisoformat(remote_updated.replace('Z', '+00:00'))
                        
                        if remote_dt > local_dt:
                            merged_data[field] = conflict.remote_data[field]
                    except ValueError:
                        # Keep local value if timestamp parsing fails
                        pass
            
            # Update metadata
            merged_data["updated_at"] = datetime.utcnow().isoformat()
            
            return merged_data
            
        except Exception as e:
            logger.error(f"Error merging records: {e}")
            return conflict.remote_data  # Fallback to remote
    
    async def _apply_resolution(self, conflict: Conflict):
        """Apply conflict resolution to the database."""
        try:
            # TODO: Implement database update with resolved data
            logger.info(f"Applied resolution for conflict {conflict.id}")
            
        except Exception as e:
            logger.error(f"Error applying resolution: {e}")
            raise
    
    def get_pending_conflicts(self) -> List[Dict[str, Any]]:
        """Get all pending conflicts."""
        pending = []
        
        for conflict in self.conflicts.values():
            if not conflict.resolved:
                pending.append({
                    "id": conflict.id,
                    "table_name": conflict.table_name,
                    "record_id": conflict.record_id,
                    "conflict_type": conflict.conflict_type.value,
                    "conflicting_fields": conflict.conflicting_fields,
                    "timestamp": conflict.timestamp.isoformat(),
                    "local_data": conflict.local_data,
                    "remote_data": conflict.remote_data
                })
        
        return pending
    
    def get_conflict_statistics(self) -> Dict[str, Any]:
        """Get conflict resolution statistics."""
        total_conflicts = len(self.conflicts)
        resolved_conflicts = len([c for c in self.conflicts.values() if c.resolved])
        pending_conflicts = total_conflicts - resolved_conflicts
        
        # Group by table
        by_table = {}
        for conflict in self.conflicts.values():
            table = conflict.table_name
            if table not in by_table:
                by_table[table] = {"total": 0, "resolved": 0, "pending": 0}
            
            by_table[table]["total"] += 1
            if conflict.resolved:
                by_table[table]["resolved"] += 1
            else:
                by_table[table]["pending"] += 1
        
        return {
            "total_conflicts": total_conflicts,
            "resolved_conflicts": resolved_conflicts,
            "pending_conflicts": pending_conflicts,
            "resolution_rate": (resolved_conflicts / total_conflicts * 100) if total_conflicts > 0 else 0,
            "by_table": by_table
        }

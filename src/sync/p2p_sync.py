"""
Peer-to-peer synchronization system.
"""

import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from sqlalchemy.orm import Session
from loguru import logger

from ..config import get_settings


@dataclass
class SyncMessage:
    """P2P synchronization message."""
    id: str
    type: str  # sync_request, sync_response, data_update, heartbeat
    sender_id: str
    timestamp: datetime
    data: Dict[str, Any]
    checksum: str


@dataclass
class PeerInfo:
    """Information about a peer node."""
    id: str
    address: str
    port: int
    last_seen: datetime
    version: str
    company_id: str
    status: str  # online, offline, syncing


@dataclass
class P2PSyncResult:
    """P2P synchronization result."""
    success: bool
    records_synced: int
    conflicts: int
    errors: List[str]
    peers_contacted: int
    duration: float


class P2PSync:
    """Peer-to-peer synchronization manager."""
    
    def __init__(self, session: Session):
        self.session = session
        self.settings = get_settings()
        
        # Node identification
        self.node_id = str(uuid.uuid4())
        self.company_id = self.settings.company_id
        
        # Network configuration
        self.port = self.settings.p2p_port
        self.discovery_port = self.settings.p2p_discovery_port
        self.max_peers = self.settings.p2p_max_peers
        
        # State
        self.peers: Dict[str, PeerInfo] = {}
        self.running = False
        self.server: Optional[asyncio.Server] = None
        self.discovery_server: Optional[asyncio.Server] = None
        
        # Sync state
        self.last_sync_timestamp: Optional[datetime] = None
        self.sync_in_progress = False
        
        # Message handlers
        self.message_handlers = {
            "sync_request": self._handle_sync_request,
            "sync_response": self._handle_sync_response,
            "data_update": self._handle_data_update,
            "heartbeat": self._handle_heartbeat,
            "peer_discovery": self._handle_peer_discovery
        }
    
    async def start(self):
        """Start P2P synchronization service."""
        if self.running:
            return
        
        try:
            self.running = True
            logger.info(f"Starting P2P sync service on port {self.port}")
            
            # Start main P2P server
            self.server = await asyncio.start_server(
                self._handle_connection,
                "0.0.0.0",
                self.port
            )
            
            # Start peer discovery server
            self.discovery_server = await asyncio.start_server(
                self._handle_discovery,
                "0.0.0.0",
                self.discovery_port
            )
            
            # Start background tasks
            asyncio.create_task(self._peer_discovery_loop())
            asyncio.create_task(self._heartbeat_loop())
            asyncio.create_task(self._cleanup_loop())
            
            logger.info("P2P sync service started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start P2P sync service: {e}")
            self.running = False
            raise
    
    async def stop(self):
        """Stop P2P synchronization service."""
        if not self.running:
            return
        
        self.running = False
        logger.info("Stopping P2P sync service")
        
        # Close servers
        if self.server:
            self.server.close()
            await self.server.wait_closed()
        
        if self.discovery_server:
            self.discovery_server.close()
            await self.discovery_server.wait_closed()
        
        # Clear peers
        self.peers.clear()
        
        logger.info("P2P sync service stopped")
    
    async def sync(self) -> P2PSyncResult:
        """Perform P2P synchronization with all peers."""
        if self.sync_in_progress:
            return P2PSyncResult(
                success=False,
                records_synced=0,
                conflicts=0,
                errors=["Sync already in progress"],
                peers_contacted=0,
                duration=0.0
            )
        
        start_time = datetime.utcnow()
        self.sync_in_progress = True
        
        try:
            logger.info("Starting P2P synchronization")
            
            # Get active peers
            active_peers = [
                peer for peer in self.peers.values()
                if peer.status == "online" and 
                   datetime.utcnow() - peer.last_seen < timedelta(minutes=5)
            ]
            
            if not active_peers:
                logger.warning("No active peers found for synchronization")
                return P2PSyncResult(
                    success=True,
                    records_synced=0,
                    conflicts=0,
                    errors=[],
                    peers_contacted=0,
                    duration=0.0
                )
            
            # Sync with each peer
            total_records = 0
            total_conflicts = 0
            errors = []
            peers_contacted = 0
            
            for peer in active_peers:
                try:
                    result = await self._sync_with_peer(peer)
                    total_records += result["records_synced"]
                    total_conflicts += result["conflicts"]
                    peers_contacted += 1
                    
                except Exception as e:
                    logger.error(f"Failed to sync with peer {peer.id}: {e}")
                    errors.append(f"Peer {peer.id}: {str(e)}")
            
            # Update sync timestamp
            self.last_sync_timestamp = start_time
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            return P2PSyncResult(
                success=len(errors) == 0,
                records_synced=total_records,
                conflicts=total_conflicts,
                errors=errors,
                peers_contacted=peers_contacted,
                duration=duration
            )
            
        except Exception as e:
            logger.error(f"P2P sync failed: {e}")
            return P2PSyncResult(
                success=False,
                records_synced=0,
                conflicts=0,
                errors=[str(e)],
                peers_contacted=0,
                duration=(datetime.utcnow() - start_time).total_seconds()
            )
        finally:
            self.sync_in_progress = False
    
    async def _sync_with_peer(self, peer: PeerInfo) -> Dict[str, Any]:
        """Synchronize with a specific peer."""
        logger.debug(f"Syncing with peer {peer.id} at {peer.address}:{peer.port}")
        
        try:
            # Connect to peer
            reader, writer = await asyncio.open_connection(peer.address, peer.port)
            
            # Send sync request
            sync_request = SyncMessage(
                id=str(uuid.uuid4()),
                type="sync_request",
                sender_id=self.node_id,
                timestamp=datetime.utcnow(),
                data={
                    "last_sync": self.last_sync_timestamp.isoformat() if self.last_sync_timestamp else None,
                    "company_id": self.company_id
                },
                checksum=""
            )
            
            await self._send_message(writer, sync_request)
            
            # Receive sync response
            response = await self._receive_message(reader)
            
            if response and response.type == "sync_response":
                # Process sync data
                return await self._process_sync_data(response.data)
            else:
                raise Exception("Invalid sync response")
                
        except Exception as e:
            logger.error(f"Sync with peer {peer.id} failed: {e}")
            raise
        finally:
            if 'writer' in locals():
                writer.close()
                await writer.wait_closed()
    
    async def _process_sync_data(self, sync_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process synchronization data from peer."""
        records_synced = 0
        conflicts = 0
        
        try:
            # Process each table's data
            for table_name, records in sync_data.get("tables", {}).items():
                for record in records:
                    try:
                        # Check if record exists locally
                        local_record = await self._get_local_record(table_name, record["id"])
                        
                        if local_record:
                            # Check for conflicts
                            if self._has_conflict(local_record, record):
                                conflicts += 1
                                await self._handle_conflict(table_name, local_record, record)
                            else:
                                # Update if remote is newer
                                if record["updated_at"] > local_record["updated_at"]:
                                    await self._update_local_record(table_name, record)
                                    records_synced += 1
                        else:
                            # Insert new record
                            await self._insert_local_record(table_name, record)
                            records_synced += 1
                            
                    except Exception as e:
                        logger.error(f"Failed to process record {record.get('id')}: {e}")
            
            return {
                "records_synced": records_synced,
                "conflicts": conflicts
            }
            
        except Exception as e:
            logger.error(f"Failed to process sync data: {e}")
            raise
    
    async def _handle_connection(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """Handle incoming P2P connection."""
        peer_address = writer.get_extra_info('peername')[0]
        logger.debug(f"New P2P connection from {peer_address}")
        
        try:
            while True:
                message = await self._receive_message(reader)
                if not message:
                    break
                
                # Handle message
                handler = self.message_handlers.get(message.type)
                if handler:
                    response = await handler(message)
                    if response:
                        await self._send_message(writer, response)
                else:
                    logger.warning(f"Unknown message type: {message.type}")
                    
        except Exception as e:
            logger.error(f"Connection error with {peer_address}: {e}")
        finally:
            writer.close()
            await writer.wait_closed()
    
    async def _handle_discovery(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """Handle peer discovery requests."""
        peer_address = writer.get_extra_info('peername')[0]
        
        try:
            # Send our node information
            node_info = {
                "id": self.node_id,
                "address": peer_address,
                "port": self.port,
                "version": "0.1.0",
                "company_id": self.company_id,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            data = json.dumps(node_info).encode()
            writer.write(len(data).to_bytes(4, 'big'))
            writer.write(data)
            await writer.drain()
            
        except Exception as e:
            logger.error(f"Discovery error with {peer_address}: {e}")
        finally:
            writer.close()
            await writer.wait_closed()
    
    async def _send_message(self, writer: asyncio.StreamWriter, message: SyncMessage):
        """Send message to peer."""
        try:
            data = json.dumps(asdict(message), default=str).encode()
            writer.write(len(data).to_bytes(4, 'big'))
            writer.write(data)
            await writer.drain()
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise
    
    async def _receive_message(self, reader: asyncio.StreamReader) -> Optional[SyncMessage]:
        """Receive message from peer."""
        try:
            # Read message length
            length_data = await reader.read(4)
            if not length_data:
                return None
            
            message_length = int.from_bytes(length_data, 'big')
            
            # Read message data
            message_data = await reader.read(message_length)
            if not message_data:
                return None
            
            # Parse message
            data = json.loads(message_data.decode())
            return SyncMessage(**data)
            
        except Exception as e:
            logger.error(f"Failed to receive message: {e}")
            return None
    
    async def _peer_discovery_loop(self):
        """Background peer discovery loop."""
        while self.running:
            try:
                await self._discover_peers()
                await asyncio.sleep(60)  # Discover every minute
            except Exception as e:
                logger.error(f"Peer discovery error: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _heartbeat_loop(self):
        """Background heartbeat loop."""
        while self.running:
            try:
                await self._send_heartbeats()
                await asyncio.sleep(30)  # Heartbeat every 30 seconds
            except Exception as e:
                logger.error(f"Heartbeat error: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_loop(self):
        """Background cleanup loop."""
        while self.running:
            try:
                await self._cleanup_stale_peers()
                await asyncio.sleep(300)  # Cleanup every 5 minutes
            except Exception as e:
                logger.error(f"Cleanup error: {e}")
                await asyncio.sleep(600)
    
    async def check_connectivity(self) -> bool:
        """Check P2P connectivity."""
        return len([p for p in self.peers.values() if p.status == "online"]) > 0
    
    async def reset_sync_state(self):
        """Reset synchronization state."""
        self.last_sync_timestamp = None
        logger.info("P2P sync state reset")
    
    def get_status(self) -> Dict[str, Any]:
        """Get P2P sync status."""
        active_peers = len([p for p in self.peers.values() if p.status == "online"])
        
        return {
            "running": self.running,
            "node_id": self.node_id,
            "port": self.port,
            "total_peers": len(self.peers),
            "active_peers": active_peers,
            "last_sync": self.last_sync_timestamp.isoformat() if self.last_sync_timestamp else None,
            "sync_in_progress": self.sync_in_progress
        }
    
    # Placeholder methods for database operations
    async def _get_local_record(self, table_name: str, record_id: str) -> Optional[Dict[str, Any]]:
        """Get local record by ID."""
        # TODO: Implement database query
        return None
    
    async def _update_local_record(self, table_name: str, record: Dict[str, Any]):
        """Update local record."""
        # TODO: Implement database update
        pass
    
    async def _insert_local_record(self, table_name: str, record: Dict[str, Any]):
        """Insert new local record."""
        # TODO: Implement database insert
        pass
    
    def _has_conflict(self, local_record: Dict[str, Any], remote_record: Dict[str, Any]) -> bool:
        """Check if there's a conflict between local and remote records."""
        # TODO: Implement conflict detection logic
        return False
    
    async def _handle_conflict(self, table_name: str, local_record: Dict[str, Any], remote_record: Dict[str, Any]):
        """Handle record conflict."""
        # TODO: Implement conflict handling
        pass
    
    # Message handlers (placeholders)
    async def _handle_sync_request(self, message: SyncMessage) -> Optional[SyncMessage]:
        """Handle sync request from peer."""
        # TODO: Implement sync request handling
        return None
    
    async def _handle_sync_response(self, message: SyncMessage) -> Optional[SyncMessage]:
        """Handle sync response from peer."""
        # TODO: Implement sync response handling
        return None
    
    async def _handle_data_update(self, message: SyncMessage) -> Optional[SyncMessage]:
        """Handle data update from peer."""
        # TODO: Implement data update handling
        return None
    
    async def _handle_heartbeat(self, message: SyncMessage) -> Optional[SyncMessage]:
        """Handle heartbeat from peer."""
        # TODO: Implement heartbeat handling
        return None
    
    async def _handle_peer_discovery(self, message: SyncMessage) -> Optional[SyncMessage]:
        """Handle peer discovery message."""
        # TODO: Implement peer discovery handling
        return None
    
    async def _discover_peers(self):
        """Discover new peers."""
        # TODO: Implement peer discovery
        pass
    
    async def _send_heartbeats(self):
        """Send heartbeats to all peers."""
        # TODO: Implement heartbeat sending
        pass
    
    async def _cleanup_stale_peers(self):
        """Remove stale peers."""
        # TODO: Implement peer cleanup
        pass

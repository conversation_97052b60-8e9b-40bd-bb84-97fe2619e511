"""
Main synchronization manager for coordinating P2P and central sync.
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from enum import Enum
from dataclasses import dataclass
from sqlalchemy.orm import Session
from loguru import logger

from ..config import get_settings
from .p2p_sync import P2PSync
from .central_sync import CentralSync
from .conflict_resolver import ConflictResolver


class SyncMode(Enum):
    """Synchronization modes."""
    P2P_ONLY = "p2p_only"
    CENTRAL_ONLY = "central_only"
    HYBRID = "hybrid"
    AUTO = "auto"


class SyncStatus(Enum):
    """Synchronization status."""
    IDLE = "idle"
    SYNCING = "syncing"
    ERROR = "error"
    CONFLICT = "conflict"


@dataclass
class SyncResult:
    """Synchronization result."""
    success: bool
    mode: SyncMode
    records_synced: int
    conflicts: int
    errors: List[str]
    duration: float
    timestamp: datetime


class SyncManager:
    """Main synchronization manager."""
    
    def __init__(self, session: Session):
        self.session = session
        self.settings = get_settings()
        
        # Sync components
        self.p2p_sync = P2PSync(session)
        self.central_sync = CentralSync(session)
        self.conflict_resolver = ConflictResolver(session)
        
        # State
        self.status = SyncStatus.IDLE
        self.current_mode = SyncMode.AUTO
        self.last_sync: Optional[datetime] = None
        self.sync_interval = timedelta(minutes=15)  # Default sync interval
        
        # Statistics
        self.sync_history: List[SyncResult] = []
        self.total_syncs = 0
        self.successful_syncs = 0
        
        # Background task
        self._sync_task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start(self):
        """Start the synchronization manager."""
        if self._running:
            return
        
        self._running = True
        logger.info("Starting synchronization manager")
        
        # Initialize sync components
        await self.p2p_sync.start()
        await self.central_sync.start()
        
        # Start background sync task
        self._sync_task = asyncio.create_task(self._background_sync_loop())
        
        logger.info("Synchronization manager started")
    
    async def stop(self):
        """Stop the synchronization manager."""
        if not self._running:
            return
        
        self._running = False
        logger.info("Stopping synchronization manager")
        
        # Cancel background task
        if self._sync_task:
            self._sync_task.cancel()
            try:
                await self._sync_task
            except asyncio.CancelledError:
                pass
        
        # Stop sync components
        await self.p2p_sync.stop()
        await self.central_sync.stop()
        
        logger.info("Synchronization manager stopped")
    
    async def sync_now(self, mode: Optional[SyncMode] = None) -> SyncResult:
        """Perform immediate synchronization."""
        if self.status == SyncStatus.SYNCING:
            logger.warning("Sync already in progress")
            return SyncResult(
                success=False,
                mode=mode or self.current_mode,
                records_synced=0,
                conflicts=0,
                errors=["Sync already in progress"],
                duration=0.0,
                timestamp=datetime.utcnow()
            )
        
        sync_mode = mode or self.current_mode
        start_time = datetime.utcnow()
        
        try:
            self.status = SyncStatus.SYNCING
            logger.info(f"Starting sync with mode: {sync_mode}")
            
            # Determine sync strategy
            if sync_mode == SyncMode.AUTO:
                sync_mode = await self._determine_best_sync_mode()
            
            # Perform synchronization
            result = await self._perform_sync(sync_mode)
            
            # Update statistics
            self.total_syncs += 1
            if result.success:
                self.successful_syncs += 1
                self.last_sync = start_time
            
            # Store result
            self.sync_history.append(result)
            
            # Keep only last 100 results
            if len(self.sync_history) > 100:
                self.sync_history = self.sync_history[-100:]
            
            self.status = SyncStatus.IDLE
            logger.info(f"Sync completed: {result.success}, records: {result.records_synced}")
            
            return result
            
        except Exception as e:
            self.status = SyncStatus.ERROR
            logger.error(f"Sync failed: {e}")
            
            return SyncResult(
                success=False,
                mode=sync_mode,
                records_synced=0,
                conflicts=0,
                errors=[str(e)],
                duration=(datetime.utcnow() - start_time).total_seconds(),
                timestamp=start_time
            )
    
    async def _background_sync_loop(self):
        """Background synchronization loop."""
        while self._running:
            try:
                # Check if it's time to sync
                if self._should_sync():
                    await self.sync_now()
                
                # Wait before next check
                await asyncio.sleep(60)  # Check every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Background sync error: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    def _should_sync(self) -> bool:
        """Check if synchronization should be performed."""
        if self.status != SyncStatus.IDLE:
            return False
        
        if not self.last_sync:
            return True
        
        return datetime.utcnow() - self.last_sync >= self.sync_interval
    
    async def _determine_best_sync_mode(self) -> SyncMode:
        """Determine the best synchronization mode based on current conditions."""
        # Check P2P connectivity
        p2p_available = await self.p2p_sync.check_connectivity()
        
        # Check central server connectivity
        central_available = await self.central_sync.check_connectivity()
        
        if p2p_available and central_available:
            # Both available - use hybrid mode
            return SyncMode.HYBRID
        elif p2p_available:
            # Only P2P available
            return SyncMode.P2P_ONLY
        elif central_available:
            # Only central available
            return SyncMode.CENTRAL_ONLY
        else:
            # No connectivity - return current mode
            logger.warning("No sync connectivity available")
            return SyncMode.P2P_ONLY  # Default fallback
    
    async def _perform_sync(self, mode: SyncMode) -> SyncResult:
        """Perform synchronization with specified mode."""
        start_time = datetime.utcnow()
        total_records = 0
        total_conflicts = 0
        errors = []
        
        try:
            if mode == SyncMode.P2P_ONLY:
                # P2P synchronization only
                p2p_result = await self.p2p_sync.sync()
                total_records += p2p_result.records_synced
                total_conflicts += p2p_result.conflicts
                errors.extend(p2p_result.errors)
                
            elif mode == SyncMode.CENTRAL_ONLY:
                # Central synchronization only
                central_result = await self.central_sync.sync()
                total_records += central_result.records_synced
                total_conflicts += central_result.conflicts
                errors.extend(central_result.errors)
                
            elif mode == SyncMode.HYBRID:
                # Hybrid synchronization
                # First sync with central server
                central_result = await self.central_sync.sync()
                total_records += central_result.records_synced
                total_conflicts += central_result.conflicts
                errors.extend(central_result.errors)
                
                # Then sync with P2P network
                p2p_result = await self.p2p_sync.sync()
                total_records += p2p_result.records_synced
                total_conflicts += p2p_result.conflicts
                errors.extend(p2p_result.errors)
            
            # Resolve any conflicts
            if total_conflicts > 0:
                await self.conflict_resolver.resolve_all_conflicts()
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            return SyncResult(
                success=len(errors) == 0,
                mode=mode,
                records_synced=total_records,
                conflicts=total_conflicts,
                errors=errors,
                duration=duration,
                timestamp=start_time
            )
            
        except Exception as e:
            duration = (datetime.utcnow() - start_time).total_seconds()
            logger.error(f"Sync performance error: {e}")
            
            return SyncResult(
                success=False,
                mode=mode,
                records_synced=total_records,
                conflicts=total_conflicts,
                errors=errors + [str(e)],
                duration=duration,
                timestamp=start_time
            )
    
    def set_sync_mode(self, mode: SyncMode):
        """Set synchronization mode."""
        self.current_mode = mode
        logger.info(f"Sync mode changed to: {mode}")
    
    def set_sync_interval(self, interval: timedelta):
        """Set synchronization interval."""
        self.sync_interval = interval
        logger.info(f"Sync interval changed to: {interval}")
    
    def get_sync_status(self) -> Dict[str, Any]:
        """Get current synchronization status."""
        return {
            "status": self.status.value,
            "mode": self.current_mode.value,
            "last_sync": self.last_sync.isoformat() if self.last_sync else None,
            "next_sync": (self.last_sync + self.sync_interval).isoformat() if self.last_sync else None,
            "total_syncs": self.total_syncs,
            "successful_syncs": self.successful_syncs,
            "success_rate": (self.successful_syncs / self.total_syncs * 100) if self.total_syncs > 0 else 0,
            "p2p_status": self.p2p_sync.get_status(),
            "central_status": self.central_sync.get_status()
        }
    
    def get_sync_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get synchronization history."""
        return [
            {
                "success": result.success,
                "mode": result.mode.value,
                "records_synced": result.records_synced,
                "conflicts": result.conflicts,
                "errors": result.errors,
                "duration": result.duration,
                "timestamp": result.timestamp.isoformat()
            }
            for result in self.sync_history[-limit:]
        ]
    
    async def force_full_sync(self) -> SyncResult:
        """Force a full synchronization of all data."""
        logger.info("Starting forced full synchronization")
        
        # Reset sync timestamps to force full sync
        await self.p2p_sync.reset_sync_state()
        await self.central_sync.reset_sync_state()
        
        # Perform sync
        return await self.sync_now(SyncMode.HYBRID)
    
    async def resolve_conflicts_manually(self, conflict_resolutions: Dict[str, str]) -> bool:
        """Resolve conflicts manually with user decisions."""
        try:
            return await self.conflict_resolver.resolve_conflicts_manually(conflict_resolutions)
        except Exception as e:
            logger.error(f"Manual conflict resolution failed: {e}")
            return False

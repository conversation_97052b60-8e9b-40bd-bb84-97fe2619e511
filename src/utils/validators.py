"""
Validation functions for various data types.
"""

import re
from typing import Optional, Union
from datetime import datetime, date


def validate_email(email: str) -> bool:
    """Validate email address format."""
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_phone(phone: str, country_code: str = "SA") -> bool:
    """Validate phone number format."""
    if not phone:
        return False
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    if country_code == "SA":
        # Saudi phone number validation
        # Should be 9 digits after removing country code and leading zero
        if digits_only.startswith("966"):
            digits_only = digits_only[3:]
        elif digits_only.startswith("0"):
            digits_only = digits_only[1:]
        
        # Saudi mobile numbers start with 5 and are 9 digits total
        return len(digits_only) == 9 and digits_only.startswith('5')
    
    # Generic phone validation (7-15 digits)
    return 7 <= len(digits_only) <= 15


def validate_vat_number(vat_number: str, country_code: str = "SA") -> bool:
    """Validate VAT number format."""
    if not vat_number:
        return False
    
    # Remove spaces and special characters
    clean_vat = re.sub(r'[^\d]', '', vat_number)
    
    if country_code == "SA":
        # Saudi VAT number is 15 digits
        return len(clean_vat) == 15 and clean_vat.isdigit()
    
    # Generic VAT validation (5-15 digits)
    return 5 <= len(clean_vat) <= 15 and clean_vat.isdigit()


def validate_cr_number(cr_number: str) -> bool:
    """Validate Commercial Registration number (Saudi Arabia)."""
    if not cr_number:
        return False
    
    # Remove spaces and special characters
    clean_cr = re.sub(r'[^\d]', '', cr_number)
    
    # Saudi CR number is 10 digits
    return len(clean_cr) == 10 and clean_cr.isdigit()


def validate_iban(iban: str, country_code: str = "SA") -> bool:
    """Validate IBAN format."""
    if not iban:
        return False
    
    # Remove spaces and convert to uppercase
    clean_iban = re.sub(r'\s', '', iban.upper())
    
    if country_code == "SA":
        # Saudi IBAN format: SA followed by 22 digits
        pattern = r'^SA\d{22}$'
        return bool(re.match(pattern, clean_iban))
    
    # Generic IBAN validation (basic format check)
    pattern = r'^[A-Z]{2}\d{2}[A-Z0-9]{4,30}$'
    return bool(re.match(pattern, clean_iban))


def validate_barcode(barcode: str) -> bool:
    """Validate barcode format."""
    if not barcode:
        return False
    
    # Remove any non-digit characters
    digits = re.sub(r'\D', '', barcode)
    
    # Check common barcode lengths
    valid_lengths = [8, 12, 13, 14]  # EAN-8, UPC-A, EAN-13, ITF-14
    
    return len(digits) in valid_lengths


def validate_product_code(code: str) -> bool:
    """Validate product code format."""
    if not code:
        return False
    
    # Product code should be alphanumeric, 3-20 characters
    pattern = r'^[A-Za-z0-9\-_]{3,20}$'
    return bool(re.match(pattern, code))


def validate_oem_number(oem_number: str) -> bool:
    """Validate OEM number format."""
    if not oem_number:
        return False
    
    # OEM numbers can contain letters, numbers, hyphens, and spaces
    # Length should be between 3 and 30 characters
    pattern = r'^[A-Za-z0-9\-\s]{3,30}$'
    return bool(re.match(pattern, oem_number.strip()))


def validate_price(price: Union[str, int, float]) -> bool:
    """Validate price value."""
    try:
        price_float = float(price)
        return price_float >= 0
    except (ValueError, TypeError):
        return False


def validate_quantity(quantity: Union[str, int, float]) -> bool:
    """Validate quantity value."""
    try:
        qty_float = float(quantity)
        return qty_float >= 0
    except (ValueError, TypeError):
        return False


def validate_percentage(percentage: Union[str, int, float]) -> bool:
    """Validate percentage value (0-100)."""
    try:
        pct_float = float(percentage)
        return 0 <= pct_float <= 100
    except (ValueError, TypeError):
        return False


def validate_date(date_str: str, format_str: str = "%Y-%m-%d") -> bool:
    """Validate date string format."""
    if not date_str:
        return False
    
    try:
        datetime.strptime(date_str, format_str)
        return True
    except ValueError:
        return False


def validate_required(value: Union[str, int, float, None]) -> bool:
    """Validate that a value is not empty or None."""
    if value is None:
        return False
    
    if isinstance(value, str):
        return bool(value.strip())
    
    return True


def validate_min_length(value: str, min_length: int) -> bool:
    """Validate minimum string length."""
    if not isinstance(value, str):
        return False
    
    return len(value.strip()) >= min_length


def validate_max_length(value: str, max_length: int) -> bool:
    """Validate maximum string length."""
    if not isinstance(value, str):
        return False
    
    return len(value.strip()) <= max_length


def validate_numeric_range(
    value: Union[str, int, float], 
    min_value: Optional[float] = None, 
    max_value: Optional[float] = None
) -> bool:
    """Validate numeric value within range."""
    try:
        num_value = float(value)
        
        if min_value is not None and num_value < min_value:
            return False
        
        if max_value is not None and num_value > max_value:
            return False
        
        return True
    except (ValueError, TypeError):
        return False


def validate_url(url: str) -> bool:
    """Validate URL format."""
    if not url:
        return False
    
    pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
    return bool(re.match(pattern, url))


def validate_ip_address(ip: str) -> bool:
    """Validate IP address format."""
    if not ip:
        return False
    
    pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    return bool(re.match(pattern, ip))


def validate_mac_address(mac: str) -> bool:
    """Validate MAC address format."""
    if not mac:
        return False
    
    pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
    return bool(re.match(pattern, mac))


def validate_credit_card(card_number: str) -> bool:
    """Validate credit card number using Luhn algorithm."""
    if not card_number:
        return False
    
    # Remove spaces and hyphens
    clean_number = re.sub(r'[\s\-]', '', card_number)
    
    # Check if all characters are digits
    if not clean_number.isdigit():
        return False
    
    # Check length (13-19 digits for most cards)
    if not (13 <= len(clean_number) <= 19):
        return False
    
    # Luhn algorithm
    def luhn_check(card_num):
        def digits_of(n):
            return [int(d) for d in str(n)]
        
        digits = digits_of(card_num)
        odd_digits = digits[-1::-2]
        even_digits = digits[-2::-2]
        checksum = sum(odd_digits)
        for d in even_digits:
            checksum += sum(digits_of(d * 2))
        return checksum % 10 == 0
    
    return luhn_check(clean_number)


def validate_file_extension(filename: str, allowed_extensions: list[str]) -> bool:
    """Validate file extension against allowed list."""
    if not filename or not allowed_extensions:
        return False
    
    file_ext = filename.split('.')[-1].lower() if '.' in filename else ""
    return file_ext in [ext.lower() for ext in allowed_extensions]


def validate_file_size(file_size: int, max_size: int) -> bool:
    """Validate file size against maximum allowed size."""
    return 0 <= file_size <= max_size


class ValidationError(Exception):
    """Custom validation error."""
    
    def __init__(self, message: str, field: Optional[str] = None):
        self.message = message
        self.field = field
        super().__init__(self.message)


class Validator:
    """Validation class for complex validation scenarios."""
    
    def __init__(self):
        self.errors = {}
    
    def add_error(self, field: str, message: str):
        """Add validation error for a field."""
        if field not in self.errors:
            self.errors[field] = []
        self.errors[field].append(message)
    
    def validate_field(self, field: str, value, validators: list):
        """Validate a field with multiple validators."""
        for validator in validators:
            if callable(validator):
                if not validator(value):
                    self.add_error(field, f"Validation failed for {field}")
            elif isinstance(validator, dict):
                func = validator.get('func')
                message = validator.get('message', f"Validation failed for {field}")
                if func and callable(func):
                    if not func(value):
                        self.add_error(field, message)
    
    def is_valid(self) -> bool:
        """Check if validation passed (no errors)."""
        return len(self.errors) == 0
    
    def get_errors(self) -> dict:
        """Get all validation errors."""
        return self.errors
    
    def clear_errors(self):
        """Clear all validation errors."""
        self.errors = {}

"""
Internationalization (i18n) system for Dims ERP.
Supports Arabic and English with RTL/LTR text direction.
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional, Union
from functools import lru_cache

from loguru import logger


class I18nManager:
    """Internationalization manager for multi-language support."""
    
    def __init__(self, default_locale: str = "ar", locales_dir: str = "locales"):
        self.default_locale = default_locale
        self.current_locale = default_locale
        self.locales_dir = Path(locales_dir)
        self._translations: Dict[str, Dict[str, Any]] = {}
        self._rtl_languages = {"ar", "he", "fa", "ur"}
        
        # Load all available translations
        self._load_translations()
    
    def _load_translations(self):
        """Load all translation files."""
        if not self.locales_dir.exists():
            logger.warning(f"Locales directory not found: {self.locales_dir}")
            return
        
        for locale_file in self.locales_dir.glob("*/messages.json"):
            locale = locale_file.parent.name
            try:
                with open(locale_file, "r", encoding="utf-8") as f:
                    self._translations[locale] = json.load(f)
                logger.info(f"Loaded translations for locale: {locale}")
            except Exception as e:
                logger.error(f"Failed to load translations for {locale}: {e}")
    
    def set_locale(self, locale: str) -> bool:
        """Set the current locale."""
        if locale in self._translations:
            self.current_locale = locale
            logger.info(f"Locale changed to: {locale}")
            return True
        else:
            logger.warning(f"Locale not available: {locale}")
            return False
    
    def get_available_locales(self) -> list[str]:
        """Get list of available locales."""
        return list(self._translations.keys())
    
    def is_rtl(self, locale: Optional[str] = None) -> bool:
        """Check if the locale uses right-to-left text direction."""
        locale = locale or self.current_locale
        return locale in self._rtl_languages
    
    def t(self, key: str, locale: Optional[str] = None, **kwargs) -> str:
        """
        Translate a key to the current or specified locale.
        
        Args:
            key: Translation key (e.g., "menu.sales.title")
            locale: Optional locale override
            **kwargs: Variables for string formatting
        
        Returns:
            Translated string or the key if translation not found
        """
        locale = locale or self.current_locale
        
        # Get translation from the specified locale
        translation = self._get_nested_value(
            self._translations.get(locale, {}), 
            key
        )
        
        # Fallback to default locale if not found
        if translation is None and locale != self.default_locale:
            translation = self._get_nested_value(
                self._translations.get(self.default_locale, {}), 
                key
            )
        
        # Fallback to English if still not found
        if translation is None and locale != "en":
            translation = self._get_nested_value(
                self._translations.get("en", {}), 
                key
            )
        
        # Final fallback to the key itself
        if translation is None:
            logger.warning(f"Translation not found for key: {key}")
            translation = key
        
        # Format the translation with provided variables
        if kwargs and isinstance(translation, str):
            try:
                translation = translation.format(**kwargs)
            except KeyError as e:
                logger.warning(f"Missing variable in translation {key}: {e}")
        
        return translation
    
    def _get_nested_value(self, data: Dict[str, Any], key: str) -> Optional[str]:
        """Get nested value from dictionary using dot notation."""
        keys = key.split(".")
        current = data
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return None
        
        return current if isinstance(current, str) else None
    
    def get_locale_info(self, locale: Optional[str] = None) -> Dict[str, Any]:
        """Get locale information including direction and name."""
        locale = locale or self.current_locale
        
        locale_names = {
            "ar": {"name": "العربية", "name_en": "Arabic"},
            "en": {"name": "English", "name_en": "English"},
        }
        
        return {
            "code": locale,
            "name": locale_names.get(locale, {}).get("name", locale),
            "name_en": locale_names.get(locale, {}).get("name_en", locale),
            "rtl": self.is_rtl(locale),
            "direction": "rtl" if self.is_rtl(locale) else "ltr",
        }
    
    def format_number(self, number: Union[int, float], locale: Optional[str] = None) -> str:
        """Format number according to locale conventions."""
        locale = locale or self.current_locale
        
        if locale == "ar":
            # Arabic number formatting
            formatted = f"{number:,.2f}" if isinstance(number, float) else f"{number:,}"
            # Convert to Arabic-Indic numerals if needed
            # arabic_numerals = "٠١٢٣٤٥٦٧٨٩"
            # for i, digit in enumerate("0123456789"):
            #     formatted = formatted.replace(digit, arabic_numerals[i])
            return formatted
        else:
            # English number formatting
            return f"{number:,.2f}" if isinstance(number, float) else f"{number:,}"
    
    def format_currency(self, amount: Union[int, float], locale: Optional[str] = None) -> str:
        """Format currency according to locale conventions."""
        locale = locale or self.current_locale
        formatted_amount = self.format_number(amount, locale)
        
        if locale == "ar":
            return f"{formatted_amount} ر.س"
        else:
            return f"SAR {formatted_amount}"
    
    def get_text_direction(self, locale: Optional[str] = None) -> str:
        """Get text direction for CSS."""
        return "rtl" if self.is_rtl(locale) else "ltr"
    
    def get_text_align(self, locale: Optional[str] = None) -> str:
        """Get text alignment for CSS."""
        return "right" if self.is_rtl(locale) else "left"


# Global i18n manager instance
_i18n_manager: Optional[I18nManager] = None


def get_i18n(default_locale: str = "ar") -> I18nManager:
    """Get the global i18n manager instance."""
    global _i18n_manager
    if _i18n_manager is None:
        _i18n_manager = I18nManager(default_locale)
    return _i18n_manager


@lru_cache(maxsize=1000)
def translate(key: str, locale: Optional[str] = None, **kwargs) -> str:
    """Cached translation function."""
    return get_i18n().t(key, locale, **kwargs)


# Convenience functions
def t(key: str, **kwargs) -> str:
    """Short alias for translate function."""
    return get_i18n().t(key, **kwargs)


def set_locale(locale: str) -> bool:
    """Set the global locale."""
    return get_i18n().set_locale(locale)


def get_current_locale() -> str:
    """Get the current locale."""
    return get_i18n().current_locale


def is_rtl() -> bool:
    """Check if current locale is RTL."""
    return get_i18n().is_rtl()


def get_direction() -> str:
    """Get current text direction."""
    return get_i18n().get_text_direction()


def get_align() -> str:
    """Get current text alignment."""
    return get_i18n().get_text_align()

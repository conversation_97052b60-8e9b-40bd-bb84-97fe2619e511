"""
Helper functions for common operations.
"""

import re
from datetime import datetime, date
from typing import Union, Optional
from decimal import Decimal

from .i18n import get_i18n


def format_currency(
    amount: Union[int, float, Decimal], 
    currency_code: str = "SAR",
    locale: Optional[str] = None
) -> str:
    """Format currency amount according to locale."""
    i18n = get_i18n()
    
    if isinstance(amount, Decimal):
        amount = float(amount)
    
    formatted_amount = i18n.format_number(amount, locale)
    
    locale = locale or i18n.current_locale
    
    if locale == "ar":
        return f"{formatted_amount} ر.س"
    else:
        return f"{currency_code} {formatted_amount}"


def format_date(
    date_obj: Union[datetime, date, str], 
    format_type: str = "short",
    locale: Optional[str] = None
) -> str:
    """Format date according to locale conventions."""
    i18n = get_i18n()
    locale = locale or i18n.current_locale
    
    # Convert string to datetime if needed
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.fromisoformat(date_obj.replace('Z', '+00:00'))
        except ValueError:
            return date_obj  # Return as-is if can't parse
    
    # Convert date to datetime if needed
    if isinstance(date_obj, date) and not isinstance(date_obj, datetime):
        date_obj = datetime.combine(date_obj, datetime.min.time())
    
    if locale == "ar":
        if format_type == "short":
            return date_obj.strftime("%d/%m/%Y")
        elif format_type == "long":
            return date_obj.strftime("%d %B %Y")
        elif format_type == "datetime":
            return date_obj.strftime("%d/%m/%Y %H:%M")
        else:
            return date_obj.strftime("%d/%m/%Y")
    else:
        if format_type == "short":
            return date_obj.strftime("%m/%d/%Y")
        elif format_type == "long":
            return date_obj.strftime("%B %d, %Y")
        elif format_type == "datetime":
            return date_obj.strftime("%m/%d/%Y %H:%M")
        else:
            return date_obj.strftime("%m/%d/%Y")


def format_phone(phone: str, country_code: str = "+966") -> str:
    """Format phone number with country code."""
    if not phone:
        return ""
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Handle Saudi phone numbers
    if country_code == "+966":
        if digits_only.startswith("966"):
            digits_only = digits_only[3:]
        elif digits_only.startswith("0"):
            digits_only = digits_only[1:]
        
        if len(digits_only) == 9:
            return f"+966 {digits_only[:2]} {digits_only[2:5]} {digits_only[5:]}"
    
    # Default formatting
    return f"{country_code} {digits_only}"


def format_percentage(value: Union[int, float], decimals: int = 2) -> str:
    """Format percentage value."""
    return f"{value:.{decimals}f}%"


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """Truncate text to specified length."""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def clean_string(text: str) -> str:
    """Clean string by removing extra whitespace and special characters."""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    return text


def generate_code(prefix: str = "", length: int = 6) -> str:
    """Generate a unique code with optional prefix."""
    import random
    import string
    
    # Generate random alphanumeric string
    random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
    
    if prefix:
        return f"{prefix}{random_part}"
    return random_part


def parse_search_query(query: str) -> dict:
    """Parse search query into components."""
    if not query:
        return {"terms": [], "filters": {}}
    
    terms = []
    filters = {}
    
    # Split by spaces but keep quoted strings together
    parts = re.findall(r'(?:[^\s"]+|"[^"]*")+', query)
    
    for part in parts:
        if ":" in part and not part.startswith('"'):
            # This is a filter (e.g., category:electronics)
            key, value = part.split(":", 1)
            filters[key.lower()] = value.strip('"')
        else:
            # This is a search term
            terms.append(part.strip('"'))
    
    return {"terms": terms, "filters": filters}


def calculate_tax(amount: Union[int, float, Decimal], tax_rate: float = 0.15) -> Decimal:
    """Calculate tax amount."""
    if isinstance(amount, (int, float)):
        amount = Decimal(str(amount))
    
    tax_rate_decimal = Decimal(str(tax_rate))
    return amount * tax_rate_decimal


def calculate_discount(
    amount: Union[int, float, Decimal], 
    discount: Union[int, float, Decimal],
    is_percentage: bool = True
) -> Decimal:
    """Calculate discount amount."""
    if isinstance(amount, (int, float)):
        amount = Decimal(str(amount))
    
    if isinstance(discount, (int, float)):
        discount = Decimal(str(discount))
    
    if is_percentage:
        return amount * (discount / Decimal('100'))
    else:
        return discount


def normalize_arabic_text(text: str) -> str:
    """Normalize Arabic text for better searching."""
    if not text:
        return ""
    
    # Normalize Arabic characters
    replacements = {
        'أ': 'ا', 'إ': 'ا', 'آ': 'ا',  # Alef variations
        'ة': 'ه',  # Teh marbuta to heh
        'ي': 'ى',  # Yeh variations
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    return text


def extract_numbers(text: str) -> list[str]:
    """Extract all numbers from text."""
    return re.findall(r'\d+(?:\.\d+)?', text)


def is_valid_barcode(barcode: str) -> bool:
    """Validate barcode format."""
    if not barcode:
        return False
    
    # Remove any non-digit characters
    digits = re.sub(r'\D', '', barcode)
    
    # Check common barcode lengths
    valid_lengths = [8, 12, 13, 14]  # EAN-8, UPC-A, EAN-13, ITF-14
    
    return len(digits) in valid_lengths


def generate_barcode(product_code: str) -> str:
    """Generate a simple barcode from product code."""
    # This is a simplified implementation
    # In production, you'd use a proper barcode generation library
    
    # Remove non-alphanumeric characters
    clean_code = re.sub(r'[^A-Za-z0-9]', '', product_code)
    
    # Pad or truncate to 12 digits for UPC-A format
    if len(clean_code) < 12:
        clean_code = clean_code.ljust(12, '0')
    else:
        clean_code = clean_code[:12]
    
    return clean_code


def safe_divide(numerator: Union[int, float], denominator: Union[int, float]) -> float:
    """Safely divide two numbers, returning 0 if denominator is 0."""
    try:
        return float(numerator) / float(denominator) if denominator != 0 else 0.0
    except (TypeError, ValueError):
        return 0.0


def round_currency(amount: Union[int, float, Decimal], decimals: int = 2) -> Decimal:
    """Round currency amount to specified decimal places."""
    if isinstance(amount, (int, float)):
        amount = Decimal(str(amount))
    
    return amount.quantize(Decimal('0.01'))


def get_file_extension(filename: str) -> str:
    """Get file extension from filename."""
    return filename.split('.')[-1].lower() if '.' in filename else ""


def is_image_file(filename: str) -> bool:
    """Check if file is an image based on extension."""
    image_extensions = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'}
    return get_file_extension(filename) in image_extensions


def is_document_file(filename: str) -> bool:
    """Check if file is a document based on extension."""
    doc_extensions = {'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'csv'}
    return get_file_extension(filename) in doc_extensions

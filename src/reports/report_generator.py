"""
Main report generator for creating various business reports.
"""

from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc, asc
from loguru import logger

from ..data.models import (
    Product, Customer, Supplier, Invoice, InvoiceItem,
    StockMovement, Company, User
)
from .report_templates import ReportTemplates
from .export_manager import ExportManager


class ReportType(Enum):
    """Types of reports available."""
    SALES_SUMMARY = "sales_summary"
    SALES_DETAILED = "sales_detailed"
    PURCHASE_SUMMARY = "purchase_summary"
    PURCHASE_DETAILED = "purchase_detailed"
    INVENTORY_VALUATION = "inventory_valuation"
    INVENTORY_MOVEMENT = "inventory_movement"
    CUSTOMER_STATEMENT = "customer_statement"
    SUPPLIER_STATEMENT = "supplier_statement"
    PROFIT_LOSS = "profit_loss"
    BALANCE_SHEET = "balance_sheet"
    CASH_FLOW = "cash_flow"
    TAX_REPORT = "tax_report"
    AGING_REPORT = "aging_report"
    TOP_PRODUCTS = "top_products"
    TOP_CUSTOMERS = "top_customers"


class ReportFormat(Enum):
    """Report output formats."""
    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"
    JSON = "json"
    HTML = "html"


@dataclass
class ReportParameters:
    """Report generation parameters."""
    report_type: ReportType
    date_from: datetime
    date_to: datetime
    company_id: str
    branch_id: Optional[str] = None
    customer_id: Optional[str] = None
    supplier_id: Optional[str] = None
    product_category: Optional[str] = None
    user_id: Optional[str] = None
    currency: str = "SAR"
    include_tax: bool = True
    group_by: Optional[str] = None
    sort_by: Optional[str] = None
    sort_order: str = "desc"
    limit: Optional[int] = None


@dataclass
class ReportResult:
    """Report generation result."""
    success: bool
    report_id: str
    report_type: ReportType
    data: Dict[str, Any]
    summary: Dict[str, Any]
    generated_at: datetime
    parameters: ReportParameters
    file_path: Optional[str] = None
    errors: Optional[List[str]] = None


class ReportGenerator:
    """Main report generator class."""
    
    def __init__(self, session: Session):
        self.session = session
        self.templates = ReportTemplates()
        self.export_manager = ExportManager()
        
        # Report generators mapping
        self.generators = {
            ReportType.SALES_SUMMARY: self._generate_sales_summary,
            ReportType.SALES_DETAILED: self._generate_sales_detailed,
            ReportType.PURCHASE_SUMMARY: self._generate_purchase_summary,
            ReportType.PURCHASE_DETAILED: self._generate_purchase_detailed,
            ReportType.INVENTORY_VALUATION: self._generate_inventory_valuation,
            ReportType.INVENTORY_MOVEMENT: self._generate_inventory_movement,
            ReportType.CUSTOMER_STATEMENT: self._generate_customer_statement,
            ReportType.SUPPLIER_STATEMENT: self._generate_supplier_statement,
            ReportType.PROFIT_LOSS: self._generate_profit_loss,
            ReportType.TAX_REPORT: self._generate_tax_report,
            ReportType.AGING_REPORT: self._generate_aging_report,
            ReportType.TOP_PRODUCTS: self._generate_top_products,
            ReportType.TOP_CUSTOMERS: self._generate_top_customers
        }
    
    async def generate_report(self, parameters: ReportParameters, 
                            output_format: ReportFormat = ReportFormat.JSON) -> ReportResult:
        """Generate report based on parameters."""
        try:
            logger.info(f"Generating {parameters.report_type.value} report")
            
            # Validate parameters
            validation_result = self._validate_parameters(parameters)
            if not validation_result["valid"]:
                return ReportResult(
                    success=False,
                    report_id="",
                    report_type=parameters.report_type,
                    data={},
                    summary={},
                    generated_at=datetime.utcnow(),
                    parameters=parameters,
                    errors=validation_result["errors"]
                )
            
            # Get report generator
            generator = self.generators.get(parameters.report_type)
            if not generator:
                return ReportResult(
                    success=False,
                    report_id="",
                    report_type=parameters.report_type,
                    data={},
                    summary={},
                    generated_at=datetime.utcnow(),
                    parameters=parameters,
                    errors=[f"Report type {parameters.report_type.value} not supported"]
                )
            
            # Generate report data
            report_data = await generator(parameters)
            
            # Create report result
            report_id = f"{parameters.report_type.value}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            
            result = ReportResult(
                success=True,
                report_id=report_id,
                report_type=parameters.report_type,
                data=report_data["data"],
                summary=report_data["summary"],
                generated_at=datetime.utcnow(),
                parameters=parameters
            )
            
            # Export to file if requested
            if output_format != ReportFormat.JSON:
                file_path = await self.export_manager.export_report(
                    result, output_format
                )
                result.file_path = file_path
            
            logger.info(f"Report {report_id} generated successfully")
            return result
            
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            return ReportResult(
                success=False,
                report_id="",
                report_type=parameters.report_type,
                data={},
                summary={},
                generated_at=datetime.utcnow(),
                parameters=parameters,
                errors=[str(e)]
            )
    
    async def _generate_sales_summary(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate sales summary report."""
        try:
            # Base query for sales invoices
            query = self.session.query(
                func.date(Invoice.invoice_date).label('date'),
                func.count(Invoice.id).label('invoice_count'),
                func.sum(Invoice.subtotal).label('subtotal'),
                func.sum(Invoice.tax_amount).label('tax_amount'),
                func.sum(Invoice.total_amount).label('total_amount')
            ).filter(
                and_(
                    Invoice.company_id == params.company_id,
                    Invoice.invoice_type == 'sales',
                    Invoice.status != 'cancelled',
                    Invoice.invoice_date >= params.date_from.date(),
                    Invoice.invoice_date <= params.date_to.date()
                )
            )
            
            # Apply additional filters
            if params.branch_id:
                query = query.filter(Invoice.branch_id == params.branch_id)
            
            if params.customer_id:
                query = query.filter(Invoice.customer_id == params.customer_id)
            
            # Group by date
            query = query.group_by(func.date(Invoice.invoice_date))
            query = query.order_by(desc(func.date(Invoice.invoice_date)))
            
            # Execute query
            results = query.all()
            
            # Process results
            daily_sales = []
            total_invoices = 0
            total_subtotal = 0
            total_tax = 0
            total_amount = 0
            
            for row in results:
                daily_data = {
                    "date": row.date.isoformat(),
                    "invoice_count": row.invoice_count,
                    "subtotal": float(row.subtotal or 0),
                    "tax_amount": float(row.tax_amount or 0),
                    "total_amount": float(row.total_amount or 0)
                }
                daily_sales.append(daily_data)
                
                total_invoices += row.invoice_count
                total_subtotal += float(row.subtotal or 0)
                total_tax += float(row.tax_amount or 0)
                total_amount += float(row.total_amount or 0)
            
            # Calculate averages
            days_count = len(daily_sales)
            avg_daily_sales = total_amount / days_count if days_count > 0 else 0
            avg_invoice_value = total_amount / total_invoices if total_invoices > 0 else 0
            
            return {
                "data": {
                    "daily_sales": daily_sales,
                    "period": {
                        "from": params.date_from.isoformat(),
                        "to": params.date_to.isoformat(),
                        "days": days_count
                    }
                },
                "summary": {
                    "total_invoices": total_invoices,
                    "total_subtotal": total_subtotal,
                    "total_tax": total_tax,
                    "total_amount": total_amount,
                    "avg_daily_sales": avg_daily_sales,
                    "avg_invoice_value": avg_invoice_value,
                    "currency": params.currency
                }
            }
            
        except Exception as e:
            logger.error(f"Sales summary generation failed: {e}")
            raise
    
    async def _generate_sales_detailed(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate detailed sales report."""
        try:
            # Query for detailed sales data
            query = self.session.query(
                Invoice.id,
                Invoice.number,
                Invoice.invoice_date,
                Invoice.due_date,
                Invoice.subtotal,
                Invoice.tax_amount,
                Invoice.total_amount,
                Invoice.payment_status,
                Customer.name.label('customer_name'),
                Customer.phone.label('customer_phone'),
                User.username.label('created_by')
            ).join(Customer, Invoice.customer_id == Customer.id, isouter=True)\
             .join(User, Invoice.created_by == User.id, isouter=True)\
             .filter(
                and_(
                    Invoice.company_id == params.company_id,
                    Invoice.invoice_type == 'sales',
                    Invoice.status != 'cancelled',
                    Invoice.invoice_date >= params.date_from.date(),
                    Invoice.invoice_date <= params.date_to.date()
                )
            )
            
            # Apply filters
            if params.customer_id:
                query = query.filter(Invoice.customer_id == params.customer_id)
            
            # Apply sorting
            if params.sort_by == "date":
                order_col = Invoice.invoice_date
            elif params.sort_by == "amount":
                order_col = Invoice.total_amount
            elif params.sort_by == "customer":
                order_col = Customer.name
            else:
                order_col = Invoice.invoice_date
            
            if params.sort_order == "asc":
                query = query.order_by(asc(order_col))
            else:
                query = query.order_by(desc(order_col))
            
            # Apply limit
            if params.limit:
                query = query.limit(params.limit)
            
            # Execute query
            results = query.all()
            
            # Process results
            invoices = []
            total_amount = 0
            total_tax = 0
            
            for row in results:
                invoice_data = {
                    "id": str(row.id),
                    "number": row.number,
                    "date": row.invoice_date.isoformat(),
                    "due_date": row.due_date.isoformat() if row.due_date else None,
                    "customer_name": row.customer_name or "Walk-in Customer",
                    "customer_phone": row.customer_phone,
                    "subtotal": float(row.subtotal or 0),
                    "tax_amount": float(row.tax_amount or 0),
                    "total_amount": float(row.total_amount or 0),
                    "payment_status": row.payment_status,
                    "created_by": row.created_by
                }
                invoices.append(invoice_data)
                
                total_amount += float(row.total_amount or 0)
                total_tax += float(row.tax_amount or 0)
            
            return {
                "data": {
                    "invoices": invoices,
                    "count": len(invoices)
                },
                "summary": {
                    "total_invoices": len(invoices),
                    "total_amount": total_amount,
                    "total_tax": total_tax,
                    "avg_invoice_value": total_amount / len(invoices) if invoices else 0,
                    "currency": params.currency
                }
            }
            
        except Exception as e:
            logger.error(f"Detailed sales generation failed: {e}")
            raise
    
    async def _generate_inventory_valuation(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate inventory valuation report."""
        try:
            # Query for product inventory
            query = self.session.query(
                Product.id,
                Product.name,
                Product.code,
                Product.category,
                Product.current_stock,
                Product.cost_price,
                Product.selling_price,
                (Product.current_stock * Product.cost_price).label('cost_value'),
                (Product.current_stock * Product.selling_price).label('selling_value')
            ).filter(
                and_(
                    Product.company_id == params.company_id,
                    Product.is_deleted == False,
                    Product.track_inventory == True
                )
            )
            
            # Apply category filter
            if params.product_category:
                query = query.filter(Product.category == params.product_category)
            
            # Execute query
            results = query.all()
            
            # Process results
            products = []
            total_cost_value = 0
            total_selling_value = 0
            total_items = 0
            
            for row in results:
                product_data = {
                    "id": str(row.id),
                    "name": row.name,
                    "code": row.code,
                    "category": row.category,
                    "current_stock": float(row.current_stock or 0),
                    "cost_price": float(row.cost_price or 0),
                    "selling_price": float(row.selling_price or 0),
                    "cost_value": float(row.cost_value or 0),
                    "selling_value": float(row.selling_value or 0),
                    "potential_profit": float((row.selling_value or 0) - (row.cost_value or 0))
                }
                products.append(product_data)
                
                total_cost_value += float(row.cost_value or 0)
                total_selling_value += float(row.selling_value or 0)
                total_items += float(row.current_stock or 0)
            
            # Group by category
            categories = {}
            for product in products:
                category = product["category"] or "Uncategorized"
                if category not in categories:
                    categories[category] = {
                        "products_count": 0,
                        "total_items": 0,
                        "cost_value": 0,
                        "selling_value": 0
                    }
                
                categories[category]["products_count"] += 1
                categories[category]["total_items"] += product["current_stock"]
                categories[category]["cost_value"] += product["cost_value"]
                categories[category]["selling_value"] += product["selling_value"]
            
            return {
                "data": {
                    "products": products,
                    "categories": categories
                },
                "summary": {
                    "total_products": len(products),
                    "total_items": total_items,
                    "total_cost_value": total_cost_value,
                    "total_selling_value": total_selling_value,
                    "potential_profit": total_selling_value - total_cost_value,
                    "profit_margin": ((total_selling_value - total_cost_value) / total_selling_value * 100) if total_selling_value > 0 else 0,
                    "currency": params.currency
                }
            }
            
        except Exception as e:
            logger.error(f"Inventory valuation generation failed: {e}")
            raise
    
    async def _generate_top_products(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate top products report."""
        try:
            # Query for top selling products
            query = self.session.query(
                Product.id,
                Product.name,
                Product.code,
                Product.category,
                func.sum(InvoiceItem.quantity).label('total_quantity'),
                func.sum(InvoiceItem.total_amount).label('total_sales'),
                func.count(InvoiceItem.id).label('transaction_count')
            ).join(InvoiceItem, Product.id == InvoiceItem.product_id)\
             .join(Invoice, InvoiceItem.invoice_id == Invoice.id)\
             .filter(
                and_(
                    Product.company_id == params.company_id,
                    Invoice.invoice_type == 'sales',
                    Invoice.status != 'cancelled',
                    Invoice.invoice_date >= params.date_from.date(),
                    Invoice.invoice_date <= params.date_to.date()
                )
            ).group_by(Product.id, Product.name, Product.code, Product.category)\
             .order_by(desc(func.sum(InvoiceItem.total_amount)))
            
            # Apply limit
            if params.limit:
                query = query.limit(params.limit)
            else:
                query = query.limit(50)  # Default top 50
            
            # Execute query
            results = query.all()
            
            # Process results
            products = []
            total_sales = 0
            total_quantity = 0
            
            for i, row in enumerate(results, 1):
                product_data = {
                    "rank": i,
                    "id": str(row.id),
                    "name": row.name,
                    "code": row.code,
                    "category": row.category,
                    "total_quantity": float(row.total_quantity or 0),
                    "total_sales": float(row.total_sales or 0),
                    "transaction_count": row.transaction_count,
                    "avg_sale_value": float(row.total_sales or 0) / row.transaction_count if row.transaction_count > 0 else 0
                }
                products.append(product_data)
                
                total_sales += float(row.total_sales or 0)
                total_quantity += float(row.total_quantity or 0)
            
            return {
                "data": {
                    "products": products,
                    "period": {
                        "from": params.date_from.isoformat(),
                        "to": params.date_to.isoformat()
                    }
                },
                "summary": {
                    "total_products": len(products),
                    "total_sales": total_sales,
                    "total_quantity": total_quantity,
                    "currency": params.currency
                }
            }
            
        except Exception as e:
            logger.error(f"Top products generation failed: {e}")
            raise
    
    def _validate_parameters(self, params: ReportParameters) -> Dict[str, Any]:
        """Validate report parameters."""
        errors = []
        
        # Check required fields
        if not params.company_id:
            errors.append("Company ID is required")
        
        if not params.date_from or not params.date_to:
            errors.append("Date range is required")
        
        # Check date range
        if params.date_from and params.date_to:
            if params.date_from > params.date_to:
                errors.append("Start date must be before end date")
            
            # Check if date range is too large (more than 2 years)
            if (params.date_to - params.date_from).days > 730:
                errors.append("Date range cannot exceed 2 years")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    # Placeholder methods for other report types
    async def _generate_purchase_summary(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate purchase summary report."""
        # TODO: Implement purchase summary
        return {"data": {}, "summary": {}}
    
    async def _generate_purchase_detailed(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate detailed purchase report."""
        # TODO: Implement detailed purchase
        return {"data": {}, "summary": {}}
    
    async def _generate_inventory_movement(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate inventory movement report."""
        # TODO: Implement inventory movement
        return {"data": {}, "summary": {}}
    
    async def _generate_customer_statement(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate customer statement."""
        # TODO: Implement customer statement
        return {"data": {}, "summary": {}}
    
    async def _generate_supplier_statement(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate supplier statement."""
        # TODO: Implement supplier statement
        return {"data": {}, "summary": {}}
    
    async def _generate_profit_loss(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate profit & loss report."""
        # TODO: Implement P&L
        return {"data": {}, "summary": {}}
    
    async def _generate_tax_report(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate tax report."""
        # TODO: Implement tax report
        return {"data": {}, "summary": {}}
    
    async def _generate_aging_report(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate aging report."""
        # TODO: Implement aging report
        return {"data": {}, "summary": {}}
    
    async def _generate_top_customers(self, params: ReportParameters) -> Dict[str, Any]:
        """Generate top customers report."""
        # TODO: Implement top customers
        return {"data": {}, "summary": {}}

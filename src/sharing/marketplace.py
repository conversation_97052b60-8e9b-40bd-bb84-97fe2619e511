"""
B2B Marketplace for auto parts sharing economy.
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.orm import Session
from loguru import logger

from ..data.models import Product, Company
from .partner_manager import PartnerManager
from .offer_manager import OfferManager
from .trust_system import TrustSystem


class OfferType(Enum):
    """Types of marketplace offers."""
    SELL = "sell"
    BUY = "buy"
    EXCHANGE = "exchange"
    LOAN = "loan"


class OfferStatus(Enum):
    """Status of marketplace offers."""
    ACTIVE = "active"
    PENDING = "pending"
    ACCEPTED = "accepted"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    EXPIRED = "expired"


@dataclass
class MarketplaceOffer:
    """Marketplace offer data structure."""
    id: str
    offer_type: OfferType
    company_id: str
    company_name: str
    product_id: str
    product_name: str
    product_code: str
    oem_numbers: List[str]
    quantity: float
    unit_price: Optional[float]
    total_price: Optional[float]
    currency: str
    description: str
    location: str
    delivery_options: List[str]
    payment_terms: str
    valid_until: datetime
    status: OfferStatus
    created_at: datetime
    trust_score: float
    images: List[str]
    tags: List[str]


@dataclass
class SearchFilters:
    """Search filters for marketplace."""
    offer_type: Optional[OfferType] = None
    product_category: Optional[str] = None
    manufacturer: Optional[str] = None
    oem_number: Optional[str] = None
    location: Optional[str] = None
    price_min: Optional[float] = None
    price_max: Optional[float] = None
    quantity_min: Optional[float] = None
    trust_score_min: Optional[float] = None
    delivery_options: Optional[List[str]] = None
    tags: Optional[List[str]] = None


class Marketplace:
    """B2B Marketplace for auto parts sharing economy."""
    
    def __init__(self, session: Session):
        self.session = session
        self.partner_manager = PartnerManager(session)
        self.offer_manager = OfferManager(session)
        self.trust_system = TrustSystem(session)
        
        # Cache for performance
        self._offers_cache: Dict[str, MarketplaceOffer] = {}
        self._cache_expiry = datetime.utcnow()
        self._cache_duration = timedelta(minutes=5)
    
    async def search_offers(self, query: str = "", filters: SearchFilters = None, 
                          limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """Search marketplace offers."""
        try:
            logger.info(f"Searching marketplace offers: query='{query}', limit={limit}")
            
            # Build search criteria
            search_criteria = self._build_search_criteria(query, filters)
            
            # Get offers from offer manager
            offers_data = await self.offer_manager.search_offers(
                search_criteria, limit, offset
            )
            
            # Enrich offers with additional data
            enriched_offers = []
            for offer_data in offers_data["offers"]:
                enriched_offer = await self._enrich_offer(offer_data)
                enriched_offers.append(enriched_offer)
            
            return {
                "offers": enriched_offers,
                "total": offers_data["total"],
                "has_more": offers_data["has_more"],
                "filters_applied": filters.__dict__ if filters else {},
                "search_time": offers_data.get("search_time", 0)
            }
            
        except Exception as e:
            logger.error(f"Marketplace search failed: {e}")
            return {
                "offers": [],
                "total": 0,
                "has_more": False,
                "error": str(e)
            }
    
    async def get_offer_details(self, offer_id: str, requester_company_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific offer."""
        try:
            # Get offer data
            offer = await self.offer_manager.get_offer(offer_id)
            if not offer:
                return None
            
            # Check if requester has access
            if not await self._check_offer_access(offer, requester_company_id):
                return {"error": "Access denied"}
            
            # Enrich with additional details
            enriched_offer = await self._enrich_offer_details(offer)
            
            # Add interaction history if companies have traded before
            interaction_history = await self.trust_system.get_interaction_history(
                requester_company_id, offer["company_id"]
            )
            
            enriched_offer["interaction_history"] = interaction_history
            
            return enriched_offer
            
        except Exception as e:
            logger.error(f"Failed to get offer details: {e}")
            return {"error": str(e)}
    
    async def create_offer(self, company_id: str, offer_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new marketplace offer."""
        try:
            logger.info(f"Creating marketplace offer for company {company_id}")
            
            # Validate offer data
            validation_result = await self._validate_offer_data(offer_data)
            if not validation_result["valid"]:
                return {"success": False, "errors": validation_result["errors"]}
            
            # Check company eligibility
            eligibility = await self._check_company_eligibility(company_id)
            if not eligibility["eligible"]:
                return {"success": False, "error": eligibility["reason"]}
            
            # Create offer through offer manager
            offer_result = await self.offer_manager.create_offer(company_id, offer_data)
            
            if offer_result["success"]:
                # Notify potential partners
                await self._notify_potential_partners(offer_result["offer_id"], offer_data)
                
                # Update company activity
                await self.trust_system.record_activity(company_id, "offer_created")
            
            return offer_result
            
        except Exception as e:
            logger.error(f"Failed to create offer: {e}")
            return {"success": False, "error": str(e)}
    
    async def respond_to_offer(self, offer_id: str, responder_company_id: str, 
                             response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Respond to a marketplace offer."""
        try:
            logger.info(f"Company {responder_company_id} responding to offer {offer_id}")
            
            # Get offer details
            offer = await self.offer_manager.get_offer(offer_id)
            if not offer:
                return {"success": False, "error": "Offer not found"}
            
            # Check if offer is still active
            if offer["status"] != OfferStatus.ACTIVE.value:
                return {"success": False, "error": "Offer is no longer active"}
            
            # Validate response
            validation_result = await self._validate_response(offer, response_data)
            if not validation_result["valid"]:
                return {"success": False, "errors": validation_result["errors"]}
            
            # Check trust requirements
            trust_check = await self._check_trust_requirements(
                offer["company_id"], responder_company_id, offer
            )
            if not trust_check["approved"]:
                return {"success": False, "error": trust_check["reason"]}
            
            # Process response
            response_result = await self.offer_manager.process_response(
                offer_id, responder_company_id, response_data
            )
            
            if response_result["success"]:
                # Create transaction record
                await self._create_transaction_record(offer, responder_company_id, response_data)
                
                # Update trust scores
                await self.trust_system.record_interaction(
                    offer["company_id"], responder_company_id, "offer_accepted"
                )
            
            return response_result
            
        except Exception as e:
            logger.error(f"Failed to respond to offer: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_company_offers(self, company_id: str, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all offers for a specific company."""
        try:
            offers = await self.offer_manager.get_company_offers(company_id, status)
            
            # Enrich with response counts and latest activity
            enriched_offers = []
            for offer in offers:
                enriched_offer = await self._enrich_company_offer(offer)
                enriched_offers.append(enriched_offer)
            
            return enriched_offers
            
        except Exception as e:
            logger.error(f"Failed to get company offers: {e}")
            return []
    
    async def get_marketplace_statistics(self, company_id: Optional[str] = None) -> Dict[str, Any]:
        """Get marketplace statistics."""
        try:
            # General marketplace stats
            stats = await self.offer_manager.get_marketplace_stats()
            
            # Add trust system stats
            trust_stats = await self.trust_system.get_marketplace_trust_stats()
            stats.update(trust_stats)
            
            # Company-specific stats if requested
            if company_id:
                company_stats = await self._get_company_marketplace_stats(company_id)
                stats["company_stats"] = company_stats
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get marketplace statistics: {e}")
            return {}
    
    async def get_recommendations(self, company_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get personalized offer recommendations."""
        try:
            # Get company's purchase history and preferences
            company_profile = await self._get_company_profile(company_id)
            
            # Get recommendations based on:
            # 1. Previous purchases
            # 2. Current inventory gaps
            # 3. Partner preferences
            # 4. Geographic proximity
            
            recommendations = await self._generate_recommendations(company_profile, limit)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Failed to get recommendations: {e}")
            return []
    
    def _build_search_criteria(self, query: str, filters: SearchFilters) -> Dict[str, Any]:
        """Build search criteria from query and filters."""
        criteria = {"query": query}
        
        if filters:
            if filters.offer_type:
                criteria["offer_type"] = filters.offer_type.value
            if filters.product_category:
                criteria["product_category"] = filters.product_category
            if filters.manufacturer:
                criteria["manufacturer"] = filters.manufacturer
            if filters.oem_number:
                criteria["oem_number"] = filters.oem_number
            if filters.location:
                criteria["location"] = filters.location
            if filters.price_min is not None:
                criteria["price_min"] = filters.price_min
            if filters.price_max is not None:
                criteria["price_max"] = filters.price_max
            if filters.quantity_min is not None:
                criteria["quantity_min"] = filters.quantity_min
            if filters.trust_score_min is not None:
                criteria["trust_score_min"] = filters.trust_score_min
            if filters.delivery_options:
                criteria["delivery_options"] = filters.delivery_options
            if filters.tags:
                criteria["tags"] = filters.tags
        
        return criteria
    
    async def _enrich_offer(self, offer_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich offer with additional data."""
        try:
            # Add company trust score
            trust_score = await self.trust_system.get_company_trust_score(offer_data["company_id"])
            offer_data["trust_score"] = trust_score
            
            # Add response count
            response_count = await self.offer_manager.get_offer_response_count(offer_data["id"])
            offer_data["response_count"] = response_count
            
            # Add time remaining
            if offer_data.get("valid_until"):
                valid_until = datetime.fromisoformat(offer_data["valid_until"])
                time_remaining = valid_until - datetime.utcnow()
                offer_data["time_remaining_hours"] = max(0, time_remaining.total_seconds() / 3600)
            
            return offer_data
            
        except Exception as e:
            logger.error(f"Failed to enrich offer: {e}")
            return offer_data
    
    async def _enrich_offer_details(self, offer: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich offer with detailed information."""
        # Add company details
        company_details = await self.partner_manager.get_company_details(offer["company_id"])
        offer["company_details"] = company_details
        
        # Add product details
        product_details = await self._get_product_details(offer["product_id"])
        offer["product_details"] = product_details
        
        # Add similar offers
        similar_offers = await self._find_similar_offers(offer, limit=5)
        offer["similar_offers"] = similar_offers
        
        return offer
    
    async def _check_offer_access(self, offer: Dict[str, Any], requester_company_id: str) -> bool:
        """Check if company has access to view offer details."""
        # Public offers are visible to all verified partners
        if offer.get("visibility") == "public":
            return await self.partner_manager.is_verified_partner(requester_company_id)
        
        # Private offers only visible to specific partners
        if offer.get("visibility") == "private":
            allowed_partners = offer.get("allowed_partners", [])
            return requester_company_id in allowed_partners
        
        # Partner-only offers visible to established partners
        if offer.get("visibility") == "partners":
            return await self.partner_manager.are_partners(offer["company_id"], requester_company_id)
        
        return True  # Default to public access
    
    async def _validate_offer_data(self, offer_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate offer data."""
        errors = []
        
        # Required fields
        required_fields = ["offer_type", "product_id", "quantity", "description", "valid_until"]
        for field in required_fields:
            if not offer_data.get(field):
                errors.append(f"Missing required field: {field}")
        
        # Validate offer type
        if offer_data.get("offer_type") not in [t.value for t in OfferType]:
            errors.append("Invalid offer type")
        
        # Validate quantity
        try:
            quantity = float(offer_data.get("quantity", 0))
            if quantity <= 0:
                errors.append("Quantity must be greater than 0")
        except (ValueError, TypeError):
            errors.append("Invalid quantity format")
        
        # Validate price if provided
        if offer_data.get("unit_price"):
            try:
                price = float(offer_data["unit_price"])
                if price < 0:
                    errors.append("Price cannot be negative")
            except (ValueError, TypeError):
                errors.append("Invalid price format")
        
        # Validate valid_until date
        try:
            valid_until = datetime.fromisoformat(offer_data["valid_until"])
            if valid_until <= datetime.utcnow():
                errors.append("Valid until date must be in the future")
        except (ValueError, TypeError):
            errors.append("Invalid valid until date format")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    async def _check_company_eligibility(self, company_id: str) -> Dict[str, Any]:
        """Check if company is eligible to create offers."""
        # Check if company is verified
        is_verified = await self.partner_manager.is_verified_partner(company_id)
        if not is_verified:
            return {"eligible": False, "reason": "Company not verified"}
        
        # Check trust score
        trust_score = await self.trust_system.get_company_trust_score(company_id)
        if trust_score < 3.0:  # Minimum trust score
            return {"eligible": False, "reason": "Trust score too low"}
        
        # Check for any restrictions
        restrictions = await self.trust_system.get_company_restrictions(company_id)
        if restrictions.get("marketplace_banned"):
            return {"eligible": False, "reason": "Marketplace access restricted"}
        
        return {"eligible": True}
    
    # Placeholder methods for additional functionality
    async def _notify_potential_partners(self, offer_id: str, offer_data: Dict[str, Any]):
        """Notify potential partners about new offer."""
        # TODO: Implement partner notification system
        pass
    
    async def _validate_response(self, offer: Dict[str, Any], response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate response to offer."""
        # TODO: Implement response validation
        return {"valid": True, "errors": []}
    
    async def _check_trust_requirements(self, offer_company_id: str, responder_company_id: str, 
                                      offer: Dict[str, Any]) -> Dict[str, Any]:
        """Check trust requirements for offer response."""
        # TODO: Implement trust requirement checking
        return {"approved": True}
    
    async def _create_transaction_record(self, offer: Dict[str, Any], responder_company_id: str, 
                                       response_data: Dict[str, Any]):
        """Create transaction record."""
        # TODO: Implement transaction record creation
        pass
    
    async def _enrich_company_offer(self, offer: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich company offer with additional data."""
        # TODO: Implement company offer enrichment
        return offer
    
    async def _get_company_marketplace_stats(self, company_id: str) -> Dict[str, Any]:
        """Get company-specific marketplace statistics."""
        # TODO: Implement company marketplace stats
        return {}
    
    async def _get_company_profile(self, company_id: str) -> Dict[str, Any]:
        """Get company profile for recommendations."""
        # TODO: Implement company profile retrieval
        return {}
    
    async def _generate_recommendations(self, company_profile: Dict[str, Any], limit: int) -> List[Dict[str, Any]]:
        """Generate personalized recommendations."""
        # TODO: Implement recommendation algorithm
        return []
    
    async def _get_product_details(self, product_id: str) -> Dict[str, Any]:
        """Get detailed product information."""
        # TODO: Implement product details retrieval
        return {}
    
    async def _find_similar_offers(self, offer: Dict[str, Any], limit: int) -> List[Dict[str, Any]]:
        """Find similar offers."""
        # TODO: Implement similar offer finding
        return []

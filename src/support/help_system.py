"""
Comprehensive help and documentation system.
"""

import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from loguru import logger


class HelpCategory(Enum):
    """Help content categories."""
    GETTING_STARTED = "getting_started"
    SALES = "sales"
    PURCHASES = "purchases"
    INVENTORY = "inventory"
    CUSTOMERS = "customers"
    SUPPLIERS = "suppliers"
    REPORTS = "reports"
    SETTINGS = "settings"
    INTEGRATIONS = "integrations"
    TROUBLESHOOTING = "troubleshooting"
    FAQ = "faq"
    VIDEO_TUTORIALS = "video_tutorials"


class ContentType(Enum):
    """Types of help content."""
    ARTICLE = "article"
    VIDEO = "video"
    TUTORIAL = "tutorial"
    FAQ = "faq"
    QUICK_TIP = "quick_tip"
    TROUBLESHOOTING = "troubleshooting"


@dataclass
class HelpContent:
    """Help content structure."""
    id: str
    title: str
    content: str
    category: HelpCategory
    content_type: ContentType
    tags: List[str]
    difficulty_level: str  # beginner, intermediate, advanced
    estimated_time: int  # minutes
    last_updated: str
    author: str
    views: int = 0
    helpful_votes: int = 0
    not_helpful_votes: int = 0
    related_articles: List[str] = None
    attachments: List[str] = None
    video_url: Optional[str] = None


@dataclass
class SearchResult:
    """Help search result."""
    content: HelpContent
    relevance_score: float
    matched_terms: List[str]


class HelpSystem:
    """Comprehensive help and documentation system."""
    
    def __init__(self):
        self.help_content: Dict[str, HelpContent] = {}
        self.content_index: Dict[str, List[str]] = {}  # keyword -> content_ids
        
        # Load help content
        self._load_help_content()
        
        # Build search index
        self._build_search_index()
    
    def search_help(self, query: str, category: Optional[HelpCategory] = None,
                   content_type: Optional[ContentType] = None,
                   limit: int = 10) -> List[SearchResult]:
        """Search help content."""
        try:
            logger.info(f"Searching help content for: {query}")
            
            # Normalize query
            query_terms = self._normalize_query(query)
            
            # Find matching content
            matches = []
            for content_id, content in self.help_content.items():
                # Apply filters
                if category and content.category != category:
                    continue
                
                if content_type and content.content_type != content_type:
                    continue
                
                # Calculate relevance score
                score, matched_terms = self._calculate_relevance(content, query_terms)
                
                if score > 0:
                    matches.append(SearchResult(
                        content=content,
                        relevance_score=score,
                        matched_terms=matched_terms
                    ))
            
            # Sort by relevance and return top results
            matches.sort(key=lambda x: x.relevance_score, reverse=True)
            return matches[:limit]
            
        except Exception as e:
            logger.error(f"Help search failed: {e}")
            return []
    
    def get_content(self, content_id: str) -> Optional[HelpContent]:
        """Get specific help content by ID."""
        content = self.help_content.get(content_id)
        if content:
            # Increment view count
            content.views += 1
        return content
    
    def get_category_content(self, category: HelpCategory) -> List[HelpContent]:
        """Get all content for a specific category."""
        return [
            content for content in self.help_content.values()
            if content.category == category
        ]
    
    def get_popular_content(self, limit: int = 10) -> List[HelpContent]:
        """Get most popular help content."""
        sorted_content = sorted(
            self.help_content.values(),
            key=lambda x: x.views,
            reverse=True
        )
        return sorted_content[:limit]
    
    def get_recent_content(self, limit: int = 10) -> List[HelpContent]:
        """Get recently updated content."""
        sorted_content = sorted(
            self.help_content.values(),
            key=lambda x: x.last_updated,
            reverse=True
        )
        return sorted_content[:limit]
    
    def get_related_content(self, content_id: str, limit: int = 5) -> List[HelpContent]:
        """Get related content for a specific article."""
        content = self.help_content.get(content_id)
        if not content:
            return []
        
        related = []
        
        # Get explicitly related articles
        if content.related_articles:
            for related_id in content.related_articles:
                if related_id in self.help_content:
                    related.append(self.help_content[related_id])
        
        # Find content with similar tags
        if len(related) < limit:
            for other_content in self.help_content.values():
                if other_content.id == content_id:
                    continue
                
                # Check for common tags
                common_tags = set(content.tags) & set(other_content.tags)
                if common_tags and other_content not in related:
                    related.append(other_content)
                    
                    if len(related) >= limit:
                        break
        
        return related[:limit]
    
    def vote_helpful(self, content_id: str, is_helpful: bool) -> bool:
        """Vote on content helpfulness."""
        try:
            content = self.help_content.get(content_id)
            if not content:
                return False
            
            if is_helpful:
                content.helpful_votes += 1
            else:
                content.not_helpful_votes += 1
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to record vote: {e}")
            return False
    
    def get_contextual_help(self, page: str, action: str = None) -> List[HelpContent]:
        """Get contextual help for specific page/action."""
        try:
            # Map pages to help content
            page_mappings = {
                "dashboard": ["dashboard_overview", "getting_started"],
                "sales": ["create_invoice", "manage_customers", "sales_reports"],
                "purchases": ["create_purchase_order", "manage_suppliers"],
                "inventory": ["add_product", "stock_management", "inventory_reports"],
                "customers": ["add_customer", "customer_management"],
                "suppliers": ["add_supplier", "supplier_management"],
                "reports": ["generate_reports", "export_data"],
                "settings": ["company_settings", "user_management"]
            }
            
            content_ids = page_mappings.get(page, [])
            
            # Add action-specific content
            if action:
                action_mappings = {
                    "create": ["create_" + page.rstrip('s')],
                    "edit": ["edit_" + page.rstrip('s')],
                    "delete": ["delete_" + page.rstrip('s')],
                    "export": ["export_data"],
                    "import": ["import_data"]
                }
                content_ids.extend(action_mappings.get(action, []))
            
            # Get content objects
            contextual_content = []
            for content_id in content_ids:
                if content_id in self.help_content:
                    contextual_content.append(self.help_content[content_id])
            
            return contextual_content
            
        except Exception as e:
            logger.error(f"Failed to get contextual help: {e}")
            return []
    
    def get_quick_tips(self, category: Optional[HelpCategory] = None) -> List[HelpContent]:
        """Get quick tips."""
        tips = [
            content for content in self.help_content.values()
            if content.content_type == ContentType.QUICK_TIP
        ]
        
        if category:
            tips = [tip for tip in tips if tip.category == category]
        
        return tips
    
    def get_video_tutorials(self, category: Optional[HelpCategory] = None) -> List[HelpContent]:
        """Get video tutorials."""
        videos = [
            content for content in self.help_content.values()
            if content.content_type == ContentType.VIDEO
        ]
        
        if category:
            videos = [video for video in videos if video.category == category]
        
        return videos
    
    def get_faq(self, category: Optional[HelpCategory] = None) -> List[HelpContent]:
        """Get frequently asked questions."""
        faqs = [
            content for content in self.help_content.values()
            if content.content_type == ContentType.FAQ
        ]
        
        if category:
            faqs = [faq for faq in faqs if faq.category == category]
        
        return faqs
    
    def _load_help_content(self):
        """Load help content from files."""
        try:
            # Load from JSON files in help directory
            help_dir = Path("data/help")
            if not help_dir.exists():
                self._create_default_content()
                return
            
            for file_path in help_dir.glob("*.json"):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content_data = json.load(f)
                    
                    for item in content_data:
                        content = HelpContent(
                            id=item["id"],
                            title=item["title"],
                            content=item["content"],
                            category=HelpCategory(item["category"]),
                            content_type=ContentType(item["content_type"]),
                            tags=item.get("tags", []),
                            difficulty_level=item.get("difficulty_level", "beginner"),
                            estimated_time=item.get("estimated_time", 5),
                            last_updated=item.get("last_updated", "2024-01-01"),
                            author=item.get("author", "Dims ERP Team"),
                            views=item.get("views", 0),
                            helpful_votes=item.get("helpful_votes", 0),
                            not_helpful_votes=item.get("not_helpful_votes", 0),
                            related_articles=item.get("related_articles", []),
                            attachments=item.get("attachments", []),
                            video_url=item.get("video_url")
                        )
                        
                        self.help_content[content.id] = content
            
            logger.info(f"Loaded {len(self.help_content)} help articles")
            
        except Exception as e:
            logger.error(f"Failed to load help content: {e}")
            self._create_default_content()
    
    def _create_default_content(self):
        """Create default help content."""
        default_content = [
            HelpContent(
                id="getting_started",
                title="البدء مع نظام الأبعاد",
                content="""
مرحباً بك في نظام الأبعاد لإدارة قطع غيار السيارات!

هذا الدليل سيساعدك على البدء:

1. **إعداد الشركة**: انتقل إلى الإعدادات وأدخل بيانات شركتك
2. **إضافة المنتجات**: ابدأ بإضافة منتجاتك في قسم المخزون
3. **إضافة العملاء**: أضف عملاءك في قسم العملاء
4. **إنشاء أول فاتورة**: انتقل إلى المبيعات وأنشئ فاتورتك الأولى

للمساعدة الإضافية، تواصل مع فريق الدعم الفني.
                """,
                category=HelpCategory.GETTING_STARTED,
                content_type=ContentType.TUTORIAL,
                tags=["بداية", "إعداد", "شركة"],
                difficulty_level="beginner",
                estimated_time=10,
                last_updated="2024-01-01",
                author="فريق الأبعاد"
            ),
            HelpContent(
                id="create_invoice",
                title="إنشاء فاتورة مبيعات",
                content="""
لإنشاء فاتورة مبيعات جديدة:

1. انتقل إلى قسم المبيعات
2. اضغط على "فاتورة جديدة"
3. اختر العميل أو أضف عميل جديد
4. أضف المنتجات المطلوبة
5. تأكد من صحة الأسعار والكميات
6. احفظ الفاتورة

يمكنك طباعة الفاتورة أو إرسالها عبر واتساب مباشرة.
                """,
                category=HelpCategory.SALES,
                content_type=ContentType.TUTORIAL,
                tags=["فاتورة", "مبيعات", "عميل"],
                difficulty_level="beginner",
                estimated_time=5,
                last_updated="2024-01-01",
                author="فريق الأبعاد"
            ),
            HelpContent(
                id="inventory_management",
                title="إدارة المخزون",
                content="""
نصائح لإدارة المخزون بفعالية:

1. **تحديث المخزون**: تأكد من تحديث كميات المخزون بانتظام
2. **الحد الأدنى**: اضبط الحد الأدنى لكل منتج لتجنب نفاد المخزون
3. **التقارير**: راجع تقارير المخزون دورياً
4. **الجرد**: قم بجرد دوري للتأكد من دقة البيانات

استخدم نظام الباركود لتسريع عمليات الإدخال والإخراج.
                """,
                category=HelpCategory.INVENTORY,
                content_type=ContentType.QUICK_TIP,
                tags=["مخزون", "جرد", "باركود"],
                difficulty_level="intermediate",
                estimated_time=3,
                last_updated="2024-01-01",
                author="فريق الأبعاد"
            )
        ]
        
        for content in default_content:
            self.help_content[content.id] = content
    
    def _build_search_index(self):
        """Build search index for faster searching."""
        self.content_index = {}
        
        for content_id, content in self.help_content.items():
            # Index title words
            title_words = self._normalize_query(content.title)
            for word in title_words:
                if word not in self.content_index:
                    self.content_index[word] = []
                self.content_index[word].append(content_id)
            
            # Index content words (first 100 words)
            content_words = self._normalize_query(content.content)[:100]
            for word in content_words:
                if word not in self.content_index:
                    self.content_index[word] = []
                if content_id not in self.content_index[word]:
                    self.content_index[word].append(content_id)
            
            # Index tags
            for tag in content.tags:
                tag_words = self._normalize_query(tag)
                for word in tag_words:
                    if word not in self.content_index:
                        self.content_index[word] = []
                    if content_id not in self.content_index[word]:
                        self.content_index[word].append(content_id)
    
    def _normalize_query(self, text: str) -> List[str]:
        """Normalize text for searching."""
        import re
        
        # Remove special characters and split into words
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Remove common Arabic stop words
        stop_words = {
            "في", "من", "إلى", "على", "عن", "مع", "هذا", "هذه", "ذلك", "تلك",
            "التي", "الذي", "التي", "كان", "كانت", "يكون", "تكون", "هو", "هي"
        }
        
        return [word for word in words if word not in stop_words and len(word) > 2]
    
    def _calculate_relevance(self, content: HelpContent, query_terms: List[str]) -> tuple:
        """Calculate relevance score for content."""
        score = 0
        matched_terms = []
        
        # Check title matches (higher weight)
        title_words = self._normalize_query(content.title)
        for term in query_terms:
            if term in title_words:
                score += 10
                matched_terms.append(term)
        
        # Check content matches
        content_words = self._normalize_query(content.content)
        for term in query_terms:
            if term in content_words:
                score += 5
                if term not in matched_terms:
                    matched_terms.append(term)
        
        # Check tag matches
        tag_words = []
        for tag in content.tags:
            tag_words.extend(self._normalize_query(tag))
        
        for term in query_terms:
            if term in tag_words:
                score += 7
                if term not in matched_terms:
                    matched_terms.append(term)
        
        # Boost score based on content popularity
        if content.views > 100:
            score *= 1.2
        
        if content.helpful_votes > content.not_helpful_votes:
            score *= 1.1
        
        return score, matched_terms

"""
Support ticket management system.
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.orm import Session
from loguru import logger

from ..data.models import User, Company


class TicketStatus(Enum):
    """Support ticket statuses."""
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    WAITING_CUSTOMER = "waiting_customer"
    RESOLVED = "resolved"
    CLOSED = "closed"
    CANCELLED = "cancelled"


class TicketPriority(Enum):
    """Support ticket priorities."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


class TicketCategory(Enum):
    """Support ticket categories."""
    TECHNICAL_ISSUE = "technical_issue"
    FEATURE_REQUEST = "feature_request"
    BUG_REPORT = "bug_report"
    ACCOUNT_ISSUE = "account_issue"
    BILLING_QUESTION = "billing_question"
    TRAINING_REQUEST = "training_request"
    INTEGRATION_HELP = "integration_help"
    DATA_MIGRATION = "data_migration"
    GENERAL_QUESTION = "general_question"


@dataclass
class TicketMessage:
    """Ticket message/comment structure."""
    id: str
    ticket_id: str
    sender_id: str
    sender_name: str
    sender_type: str  # customer, support, system
    message: str
    attachments: List[str]
    timestamp: datetime
    is_internal: bool = False


@dataclass
class SupportTicket:
    """Support ticket structure."""
    id: str
    number: str
    title: str
    description: str
    category: TicketCategory
    priority: TicketPriority
    status: TicketStatus
    customer_id: str
    customer_name: str
    company_id: str
    assigned_to: Optional[str]
    assigned_to_name: Optional[str]
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime]
    closed_at: Optional[datetime]
    messages: List[TicketMessage]
    tags: List[str]
    satisfaction_rating: Optional[int] = None
    satisfaction_comment: Optional[str] = None


class TicketManager:
    """Support ticket management system."""
    
    def __init__(self, session: Session):
        self.session = session
        
        # SLA definitions (in hours)
        self.sla_response_times = {
            TicketPriority.CRITICAL: 1,
            TicketPriority.URGENT: 4,
            TicketPriority.HIGH: 8,
            TicketPriority.NORMAL: 24,
            TicketPriority.LOW: 48
        }
        
        self.sla_resolution_times = {
            TicketPriority.CRITICAL: 4,
            TicketPriority.URGENT: 24,
            TicketPriority.HIGH: 72,
            TicketPriority.NORMAL: 168,  # 1 week
            TicketPriority.LOW: 336     # 2 weeks
        }
        
        # Auto-assignment rules
        self.assignment_rules = {
            TicketCategory.TECHNICAL_ISSUE: "tech_team",
            TicketCategory.BUG_REPORT: "dev_team",
            TicketCategory.FEATURE_REQUEST: "product_team",
            TicketCategory.BILLING_QUESTION: "billing_team",
            TicketCategory.ACCOUNT_ISSUE: "account_team",
            TicketCategory.INTEGRATION_HELP: "tech_team",
            TicketCategory.DATA_MIGRATION: "tech_team"
        }
    
    async def create_ticket(self, customer_id: str, company_id: str,
                          title: str, description: str,
                          category: TicketCategory,
                          priority: TicketPriority = TicketPriority.NORMAL,
                          attachments: List[str] = None) -> str:
        """Create a new support ticket."""
        try:
            # Get customer info
            customer = self.session.query(User).filter(User.id == customer_id).first()
            if not customer:
                raise ValueError("Customer not found")
            
            # Generate ticket number
            ticket_number = await self._generate_ticket_number()
            
            # Create ticket
            ticket = SupportTicket(
                id=f"ticket_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}",
                number=ticket_number,
                title=title,
                description=description,
                category=category,
                priority=priority,
                status=TicketStatus.OPEN,
                customer_id=customer_id,
                customer_name=customer.full_name or customer.username,
                company_id=company_id,
                assigned_to=None,
                assigned_to_name=None,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                resolved_at=None,
                closed_at=None,
                messages=[],
                tags=[]
            )
            
            # Add initial message
            initial_message = TicketMessage(
                id=f"msg_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}",
                ticket_id=ticket.id,
                sender_id=customer_id,
                sender_name=ticket.customer_name,
                sender_type="customer",
                message=description,
                attachments=attachments or [],
                timestamp=datetime.utcnow()
            )
            
            ticket.messages.append(initial_message)
            
            # Auto-assign if rules exist
            await self._auto_assign_ticket(ticket)
            
            # Store ticket (TODO: implement database storage)
            await self._store_ticket(ticket)
            
            # Send notifications
            await self._notify_ticket_created(ticket)
            
            logger.info(f"Support ticket {ticket_number} created for customer {customer_id}")
            
            return ticket.id
            
        except Exception as e:
            logger.error(f"Failed to create support ticket: {e}")
            raise
    
    async def add_message(self, ticket_id: str, sender_id: str,
                         message: str, attachments: List[str] = None,
                         is_internal: bool = False) -> bool:
        """Add message to ticket."""
        try:
            # Get ticket
            ticket = await self._get_ticket(ticket_id)
            if not ticket:
                return False
            
            # Get sender info
            sender = self.session.query(User).filter(User.id == sender_id).first()
            if not sender:
                return False
            
            # Determine sender type
            sender_type = "customer" if sender_id == ticket.customer_id else "support"
            
            # Create message
            ticket_message = TicketMessage(
                id=f"msg_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}",
                ticket_id=ticket_id,
                sender_id=sender_id,
                sender_name=sender.full_name or sender.username,
                sender_type=sender_type,
                message=message,
                attachments=attachments or [],
                timestamp=datetime.utcnow(),
                is_internal=is_internal
            )
            
            # Add message to ticket
            ticket.messages.append(ticket_message)
            ticket.updated_at = datetime.utcnow()
            
            # Update status if customer replied
            if sender_type == "customer" and ticket.status == TicketStatus.WAITING_CUSTOMER:
                ticket.status = TicketStatus.IN_PROGRESS
            
            # Store updated ticket
            await self._store_ticket(ticket)
            
            # Send notifications
            await self._notify_message_added(ticket, ticket_message)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add message to ticket: {e}")
            return False
    
    async def update_ticket_status(self, ticket_id: str, new_status: TicketStatus,
                                 updated_by: str, comment: str = "") -> bool:
        """Update ticket status."""
        try:
            # Get ticket
            ticket = await self._get_ticket(ticket_id)
            if not ticket:
                return False
            
            old_status = ticket.status
            ticket.status = new_status
            ticket.updated_at = datetime.utcnow()
            
            # Set resolution/closure timestamps
            if new_status == TicketStatus.RESOLVED:
                ticket.resolved_at = datetime.utcnow()
            elif new_status == TicketStatus.CLOSED:
                ticket.closed_at = datetime.utcnow()
                if not ticket.resolved_at:
                    ticket.resolved_at = datetime.utcnow()
            
            # Add system message
            if comment:
                system_message = TicketMessage(
                    id=f"msg_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}",
                    ticket_id=ticket_id,
                    sender_id=updated_by,
                    sender_name="System",
                    sender_type="system",
                    message=f"Status changed from {old_status.value} to {new_status.value}. {comment}",
                    attachments=[],
                    timestamp=datetime.utcnow()
                )
                ticket.messages.append(system_message)
            
            # Store updated ticket
            await self._store_ticket(ticket)
            
            # Send notifications
            await self._notify_status_changed(ticket, old_status, new_status)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update ticket status: {e}")
            return False
    
    async def assign_ticket(self, ticket_id: str, assigned_to: str,
                          assigned_by: str) -> bool:
        """Assign ticket to support agent."""
        try:
            # Get ticket
            ticket = await self._get_ticket(ticket_id)
            if not ticket:
                return False
            
            # Get assignee info
            assignee = self.session.query(User).filter(User.id == assigned_to).first()
            if not assignee:
                return False
            
            old_assignee = ticket.assigned_to_name
            ticket.assigned_to = assigned_to
            ticket.assigned_to_name = assignee.full_name or assignee.username
            ticket.updated_at = datetime.utcnow()
            
            # Update status if not already in progress
            if ticket.status == TicketStatus.OPEN:
                ticket.status = TicketStatus.IN_PROGRESS
            
            # Add system message
            system_message = TicketMessage(
                id=f"msg_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}",
                ticket_id=ticket_id,
                sender_id=assigned_by,
                sender_name="System",
                sender_type="system",
                message=f"Ticket assigned to {ticket.assigned_to_name}" + 
                       (f" (previously: {old_assignee})" if old_assignee else ""),
                attachments=[],
                timestamp=datetime.utcnow(),
                is_internal=True
            )
            ticket.messages.append(system_message)
            
            # Store updated ticket
            await self._store_ticket(ticket)
            
            # Send notifications
            await self._notify_ticket_assigned(ticket, assigned_to)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to assign ticket: {e}")
            return False
    
    async def get_tickets(self, filters: Dict[str, Any] = None,
                        limit: int = 50, offset: int = 0) -> List[SupportTicket]:
        """Get tickets with filters."""
        try:
            # TODO: Implement database query with filters
            # This would query the tickets table with various filters
            
            tickets = []
            return tickets
            
        except Exception as e:
            logger.error(f"Failed to get tickets: {e}")
            return []
    
    async def get_ticket_details(self, ticket_id: str) -> Optional[SupportTicket]:
        """Get detailed ticket information."""
        return await self._get_ticket(ticket_id)
    
    async def get_customer_tickets(self, customer_id: str,
                                 status: Optional[TicketStatus] = None) -> List[SupportTicket]:
        """Get all tickets for a customer."""
        try:
            filters = {"customer_id": customer_id}
            if status:
                filters["status"] = status.value
            
            return await self.get_tickets(filters)
            
        except Exception as e:
            logger.error(f"Failed to get customer tickets: {e}")
            return []
    
    async def get_agent_tickets(self, agent_id: str,
                              status: Optional[TicketStatus] = None) -> List[SupportTicket]:
        """Get all tickets assigned to an agent."""
        try:
            filters = {"assigned_to": agent_id}
            if status:
                filters["status"] = status.value
            
            return await self.get_tickets(filters)
            
        except Exception as e:
            logger.error(f"Failed to get agent tickets: {e}")
            return []
    
    async def get_ticket_statistics(self, company_id: Optional[str] = None,
                                  date_from: Optional[datetime] = None,
                                  date_to: Optional[datetime] = None) -> Dict[str, Any]:
        """Get ticket statistics."""
        try:
            # TODO: Implement statistics calculation
            
            stats = {
                "total_tickets": 0,
                "open_tickets": 0,
                "resolved_tickets": 0,
                "avg_resolution_time": 0,
                "tickets_by_category": {},
                "tickets_by_priority": {},
                "tickets_by_status": {},
                "sla_compliance": 0,
                "customer_satisfaction": 0
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get ticket statistics: {e}")
            return {}
    
    async def rate_ticket(self, ticket_id: str, rating: int,
                         comment: str = "") -> bool:
        """Rate ticket satisfaction."""
        try:
            if not 1 <= rating <= 5:
                return False
            
            ticket = await self._get_ticket(ticket_id)
            if not ticket:
                return False
            
            ticket.satisfaction_rating = rating
            ticket.satisfaction_comment = comment
            ticket.updated_at = datetime.utcnow()
            
            await self._store_ticket(ticket)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to rate ticket: {e}")
            return False
    
    async def _generate_ticket_number(self) -> str:
        """Generate unique ticket number."""
        # TODO: Implement proper ticket number generation
        return f"TKT-{datetime.utcnow().strftime('%Y%m%d')}-{datetime.utcnow().strftime('%H%M%S')}"
    
    async def _auto_assign_ticket(self, ticket: SupportTicket):
        """Auto-assign ticket based on category."""
        try:
            # TODO: Implement auto-assignment logic
            # This would assign tickets to appropriate teams/agents
            pass
            
        except Exception as e:
            logger.error(f"Auto-assignment failed: {e}")
    
    async def _store_ticket(self, ticket: SupportTicket):
        """Store ticket in database."""
        try:
            # TODO: Implement database storage
            pass
            
        except Exception as e:
            logger.error(f"Failed to store ticket: {e}")
            raise
    
    async def _get_ticket(self, ticket_id: str) -> Optional[SupportTicket]:
        """Get ticket from database."""
        try:
            # TODO: Implement database retrieval
            return None
            
        except Exception as e:
            logger.error(f"Failed to get ticket: {e}")
            return None
    
    # Notification methods (placeholders)
    async def _notify_ticket_created(self, ticket: SupportTicket):
        """Notify about new ticket creation."""
        # TODO: Implement notification system
        pass
    
    async def _notify_message_added(self, ticket: SupportTicket, message: TicketMessage):
        """Notify about new message."""
        # TODO: Implement notification system
        pass
    
    async def _notify_status_changed(self, ticket: SupportTicket, 
                                   old_status: TicketStatus, new_status: TicketStatus):
        """Notify about status change."""
        # TODO: Implement notification system
        pass
    
    async def _notify_ticket_assigned(self, ticket: SupportTicket, assigned_to: str):
        """Notify about ticket assignment."""
        # TODO: Implement notification system
        pass

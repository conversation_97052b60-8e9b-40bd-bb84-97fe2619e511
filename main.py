"""
Main entry point for Dims ERP System.
نظام الأبعاد لإدارة قطع غيار السيارات
"""

import asyncio
import sys
from pathlib import Path
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import get_settings
from src.data.database import DatabaseManager
from src.ui.main_window import MainWindow
from src.sync.sync_manager import SyncManager
from src.notifications.notification_manager import NotificationManager
from src.tools.backup_manager import BackupManager
from src.security.audit_logger import AuditLogger
from src.integrations.whatsapp import WhatsAppIntegration
from src.integrations.zatca import ZATCAIntegration


class DimsERP:
    """Main Dims ERP application class."""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Core components
        self.db_manager = None
        self.sync_manager = None
        self.notification_manager = None
        self.backup_manager = None
        self.audit_logger = None
        
        # Integrations
        self.whatsapp_integration = None
        self.zatca_integration = None
        
        # UI
        self.main_window = None
        
        # Running state
        self.running = False
    
    async def initialize(self):
        """Initialize all system components."""
        try:
            logger.info("Initializing Dims ERP System...")
            
            # Initialize database
            logger.info("Initializing database...")
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()
            
            # Get database session
            session = self.db_manager.get_session()
            
            # Initialize core components
            logger.info("Initializing core components...")
            
            self.sync_manager = SyncManager(session)
            self.notification_manager = NotificationManager(session)
            self.backup_manager = BackupManager()
            self.audit_logger = AuditLogger(session)
            
            # Initialize integrations
            logger.info("Initializing integrations...")
            
            if self.settings.whatsapp_enabled:
                self.whatsapp_integration = WhatsAppIntegration()
            
            if self.settings.zatca_enabled:
                self.zatca_integration = ZATCAIntegration()
            
            # Initialize UI
            logger.info("Initializing user interface...")
            self.main_window = MainWindow(
                db_manager=self.db_manager,
                sync_manager=self.sync_manager,
                notification_manager=self.notification_manager,
                backup_manager=self.backup_manager,
                audit_logger=self.audit_logger,
                whatsapp_integration=self.whatsapp_integration,
                zatca_integration=self.zatca_integration
            )
            
            logger.info("Dims ERP System initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Dims ERP System: {e}")
            raise
    
    async def start(self):
        """Start all system services."""
        try:
            if self.running:
                return
            
            logger.info("Starting Dims ERP System...")
            
            # Start core services
            if self.sync_manager:
                await self.sync_manager.start()
            
            if self.notification_manager:
                await self.notification_manager.start()
            
            if self.backup_manager:
                await self.backup_manager.start()
            
            # Log system startup
            if self.audit_logger:
                await self.audit_logger.log_action(
                    action="system_startup",
                    user_id=None,
                    company_id="system",
                    resource_type="system",
                    description="Dims ERP System started"
                )
            
            self.running = True
            logger.info("Dims ERP System started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start Dims ERP System: {e}")
            raise
    
    async def stop(self):
        """Stop all system services."""
        try:
            if not self.running:
                return
            
            logger.info("Stopping Dims ERP System...")
            
            # Log system shutdown
            if self.audit_logger:
                await self.audit_logger.log_action(
                    action="system_shutdown",
                    user_id=None,
                    company_id="system",
                    resource_type="system",
                    description="Dims ERP System stopping"
                )
            
            # Stop services
            if self.backup_manager:
                await self.backup_manager.stop()
            
            if self.notification_manager:
                await self.notification_manager.stop()
            
            if self.sync_manager:
                await self.sync_manager.stop()
            
            # Close integrations
            if self.whatsapp_integration:
                await self.whatsapp_integration.close()
            
            if self.zatca_integration:
                await self.zatca_integration.close()
            
            # Close database
            if self.db_manager:
                await self.db_manager.close()
            
            self.running = False
            logger.info("Dims ERP System stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping Dims ERP System: {e}")
    
    def run(self):
        """Run the application."""
        try:
            # Setup logging
            self._setup_logging()
            
            # Run async initialization and startup
            asyncio.run(self._async_run())
            
        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
        except Exception as e:
            logger.error(f"Application error: {e}")
            sys.exit(1)
    
    async def _async_run(self):
        """Async run method."""
        try:
            # Initialize system
            await self.initialize()
            
            # Start services
            await self.start()
            
            # Run UI
            if self.main_window:
                await self.main_window.run()
            
        finally:
            # Cleanup
            await self.stop()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        # Remove default logger
        logger.remove()
        
        # Add console logger
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO"
        )
        
        # Add file logger
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logger.add(
            log_dir / "dims_erp.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="DEBUG",
            rotation="10 MB",
            retention="30 days",
            compression="gz"
        )
        
        # Add error logger
        logger.add(
            log_dir / "errors.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="ERROR",
            rotation="5 MB",
            retention="90 days"
        )


def main():
    """Main entry point."""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                    نظام الأبعاد لإدارة قطع غيار السيارات                    ║
    ║                     Dims ERP - Auto Parts Management                     ║
    ║                                                              ║
    ║  • نظام شامل لإدارة قطع غيار السيارات                                      ║
    ║  • مزامنة P2P ومركزية متقدمة                                            ║
    ║  • نظام OEM متطور للبحث والمطابقة                                       ║
    ║  • اقتصاد تشاركي B2B للتجارة                                           ║
    ║  • تكامل واتساب و ZATCA                                               ║
    ║  • دعم RTL وتعدد اللغات                                               ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Create and run application
    app = DimsERP()
    app.run()


if __name__ == "__main__":
    main()
